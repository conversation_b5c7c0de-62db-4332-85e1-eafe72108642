{"name": "lightningcss-darwin-arm64", "version": "1.30.1", "license": "MPL-2.0", "description": "A CSS parser, transformer, and minifier written in Rust", "main": "lightningcss.darwin-arm64.node", "browserslist": "last 2 versions, not dead", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/lightningcss.git"}, "engines": {"node": ">= 12.0.0"}, "files": ["lightningcss.darwin-arm64.node"], "resolutions": {"lightningcss": "link:."}, "os": ["darwin"], "cpu": ["arm64"]}