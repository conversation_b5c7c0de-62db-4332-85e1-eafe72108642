var Mu=Object.create;var Br=Object.defineProperty;var Nu=Object.getOwnPropertyDescriptor;var Lu=Object.getOwnPropertyNames;var Uu=Object.getPrototypeOf,Fu=Object.prototype.hasOwnProperty;var de=(e,t)=>()=>(e&&(t=e(e=0)),t);var oe=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),qt=(e,t)=>{for(var r in t)Br(e,r,{get:t[r],enumerable:!0})},vo=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Lu(t))!Fu.call(e,i)&&i!==r&&Br(e,i,{get:()=>t[i],enumerable:!(n=Nu(t,i))||n.enumerable});return e};var _e=(e,t,r)=>(r=e!=null?Mu(Uu(e)):{},vo(t||!e||!e.__esModule?Br(r,"default",{value:e,enumerable:!0}):r,e)),$u=e=>vo(Br({},"__esModule",{value:!0}),e);function Kn(e,t){if(t=t.toLowerCase(),t==="utf8"||t==="utf-8")return new h(ju.encode(e));if(t==="base64"||t==="base64url")return e=e.replace(/-/g,"+").replace(/_/g,"/"),e=e.replace(/[^A-Za-z0-9+/]/g,""),new h([...atob(e)].map(r=>r.charCodeAt(0)));if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return new h([...e].map(r=>r.charCodeAt(0)));if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let r=new h(e.length*2),n=new DataView(r.buffer);for(let i=0;i<e.length;i++)n.setUint16(i*2,e.charCodeAt(i),!0);return r}if(t==="hex"){let r=new h(e.length/2);for(let n=0,i=0;i<e.length;i+=2,n++)r[n]=parseInt(e.slice(i,i+2),16);return r}Co(`encoding "${t}"`)}function Vu(e){let r=Object.getOwnPropertyNames(DataView.prototype).filter(a=>a.startsWith("get")||a.startsWith("set")),n=r.map(a=>a.replace("get","read").replace("set","write")),i=(a,f)=>function(T=0){return K(T,"offset"),ce(T,"offset"),Z(T,"offset",this.length-1),new DataView(this.buffer)[r[a]](T,f)},o=(a,f)=>function(T,v=0){let A=r[a].match(/set(\w+\d+)/)[1].toLowerCase(),R=Bu[A];return K(v,"offset"),ce(v,"offset"),Z(v,"offset",this.length-1),qu(T,"value",R[0],R[1]),new DataView(this.buffer)[r[a]](v,T,f),v+parseInt(r[a].match(/\d+/)[0])/8},s=a=>{a.forEach(f=>{f.includes("Uint")&&(e[f.replace("Uint","UInt")]=e[f]),f.includes("Float64")&&(e[f.replace("Float64","Double")]=e[f]),f.includes("Float32")&&(e[f.replace("Float32","Float")]=e[f])})};n.forEach((a,f)=>{a.startsWith("read")&&(e[a]=i(f,!1),e[a+"LE"]=i(f,!0),e[a+"BE"]=i(f,!1)),a.startsWith("write")&&(e[a]=o(f,!1),e[a+"LE"]=o(f,!0),e[a+"BE"]=o(f,!1)),s([a,a+"LE",a+"BE"])})}function Co(e){throw new Error(`Buffer polyfill does not implement "${e}"`)}function jr(e,t){if(!(e instanceof Uint8Array))throw new TypeError(`The "${t}" argument must be an instance of Buffer or Uint8Array`)}function Z(e,t,r=Hu+1){if(e<0||e>r){let n=new RangeError(`The value of "${t}" is out of range. It must be >= 0 && <= ${r}. Received ${e}`);throw n.code="ERR_OUT_OF_RANGE",n}}function K(e,t){if(typeof e!="number"){let r=new TypeError(`The "${t}" argument must be of type number. Received type ${typeof e}.`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function ce(e,t){if(!Number.isInteger(e)||Number.isNaN(e)){let r=new RangeError(`The value of "${t}" is out of range. It must be an integer. Received ${e}`);throw r.code="ERR_OUT_OF_RANGE",r}}function qu(e,t,r,n){if(e<r||e>n){let i=new RangeError(`The value of "${t}" is out of range. It must be >= ${r} and <= ${n}. Received ${e}`);throw i.code="ERR_OUT_OF_RANGE",i}}function Ao(e,t){if(typeof e!="string"){let r=new TypeError(`The "${t}" argument must be of type string. Received type ${typeof e}`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function Ju(e,t="utf8"){return h.from(e,t)}var h,Bu,ju,Qu,Gu,Hu,y,zn,u=de(()=>{"use strict";h=class e extends Uint8Array{_isBuffer=!0;get offset(){return this.byteOffset}static alloc(t,r=0,n="utf8"){return Ao(n,"encoding"),e.allocUnsafe(t).fill(r,n)}static allocUnsafe(t){return e.from(t)}static allocUnsafeSlow(t){return e.from(t)}static isBuffer(t){return t&&!!t._isBuffer}static byteLength(t,r="utf8"){if(typeof t=="string")return Kn(t,r).byteLength;if(t&&t.byteLength)return t.byteLength;let n=new TypeError('The "string" argument must be of type string or an instance of Buffer or ArrayBuffer.');throw n.code="ERR_INVALID_ARG_TYPE",n}static isEncoding(t){return Gu.includes(t)}static compare(t,r){jr(t,"buff1"),jr(r,"buff2");for(let n=0;n<t.length;n++){if(t[n]<r[n])return-1;if(t[n]>r[n])return 1}return t.length===r.length?0:t.length>r.length?1:-1}static from(t,r="utf8"){if(t&&typeof t=="object"&&t.type==="Buffer")return new e(t.data);if(typeof t=="number")return new e(new Uint8Array(t));if(typeof t=="string")return Kn(t,r);if(ArrayBuffer.isView(t)){let{byteOffset:n,byteLength:i,buffer:o}=t;return"map"in t&&typeof t.map=="function"?new e(t.map(s=>s%256),n,i):new e(o,n,i)}if(t&&typeof t=="object"&&("length"in t||"byteLength"in t||"buffer"in t))return new e(t);throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}static concat(t,r){if(t.length===0)return e.alloc(0);let n=[].concat(...t.map(o=>[...o])),i=e.alloc(r!==void 0?r:n.length);return i.set(r!==void 0?n.slice(0,r):n),i}slice(t=0,r=this.length){return this.subarray(t,r)}subarray(t=0,r=this.length){return Object.setPrototypeOf(super.subarray(t,r),e.prototype)}reverse(){return super.reverse(),this}readIntBE(t,r){K(t,"offset"),ce(t,"offset"),Z(t,"offset",this.length-1),K(r,"byteLength"),ce(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return n.getUint8(0)&128&&(i-=Math.pow(256,r)),i}readIntLE(t,r){K(t,"offset"),ce(t,"offset"),Z(t,"offset",this.length-1),K(r,"byteLength"),ce(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return n.getUint8(r-1)&128&&(i-=Math.pow(256,r)),i}readUIntBE(t,r){K(t,"offset"),ce(t,"offset"),Z(t,"offset",this.length-1),K(r,"byteLength"),ce(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return i}readUintBE(t,r){return this.readUIntBE(t,r)}readUIntLE(t,r){K(t,"offset"),ce(t,"offset"),Z(t,"offset",this.length-1),K(r,"byteLength"),ce(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return i}readUintLE(t,r){return this.readUIntLE(t,r)}writeIntBE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntBE(t,r,n)}writeIntLE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntLE(t,r,n)}writeUIntBE(t,r,n){K(r,"offset"),ce(r,"offset"),Z(r,"offset",this.length-1),K(n,"byteLength"),ce(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=n-1;o>=0;o--)i.setUint8(o,t&255),t=t/256;return r+n}writeUintBE(t,r,n){return this.writeUIntBE(t,r,n)}writeUIntLE(t,r,n){K(r,"offset"),ce(r,"offset"),Z(r,"offset",this.length-1),K(n,"byteLength"),ce(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=0;o<n;o++)i.setUint8(o,t&255),t=t/256;return r+n}writeUintLE(t,r,n){return this.writeUIntLE(t,r,n)}toJSON(){return{type:"Buffer",data:Array.from(this)}}swap16(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=2)t.setUint16(r,t.getUint16(r,!0),!1);return this}swap32(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=4)t.setUint32(r,t.getUint32(r,!0),!1);return this}swap64(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=8)t.setBigUint64(r,t.getBigUint64(r,!0),!1);return this}compare(t,r=0,n=t.length,i=0,o=this.length){return jr(t,"target"),K(r,"targetStart"),K(n,"targetEnd"),K(i,"sourceStart"),K(o,"sourceEnd"),Z(r,"targetStart"),Z(n,"targetEnd",t.length),Z(i,"sourceStart"),Z(o,"sourceEnd",this.length),e.compare(this.slice(i,o),t.slice(r,n))}equals(t){return jr(t,"otherBuffer"),this.length===t.length&&this.every((r,n)=>r===t[n])}copy(t,r=0,n=0,i=this.length){Z(r,"targetStart"),Z(n,"sourceStart",this.length),Z(i,"sourceEnd"),r>>>=0,n>>>=0,i>>>=0;let o=0;for(;n<i&&!(this[n]===void 0||t[r]===void 0);)t[r]=this[n],o++,n++,r++;return o}write(t,r,n,i="utf8"){let o=typeof r=="string"?0:r??0,s=typeof n=="string"?this.length-o:n??this.length-o;return i=typeof r=="string"?r:typeof n=="string"?n:i,K(o,"offset"),K(s,"length"),Z(o,"offset",this.length),Z(s,"length",this.length),(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")&&(s=s-s%2),Kn(t,i).copy(this,o,0,s)}fill(t=0,r=0,n=this.length,i="utf-8"){let o=typeof r=="string"?0:r,s=typeof n=="string"?this.length:n;if(i=typeof r=="string"?r:typeof n=="string"?n:i,t=e.from(typeof t=="number"?[t]:t??[],i),Ao(i,"encoding"),Z(o,"offset",this.length),Z(s,"end",this.length),t.length!==0)for(let a=o;a<s;a+=t.length)super.set(t.slice(0,t.length+a>=this.length?this.length-a:t.length),a);return this}includes(t,r=null,n="utf-8"){return this.indexOf(t,r,n)!==-1}lastIndexOf(t,r=null,n="utf-8"){return this.indexOf(t,r,n,!0)}indexOf(t,r=null,n="utf-8",i=!1){let o=i?this.findLastIndex.bind(this):this.findIndex.bind(this);n=typeof r=="string"?r:n;let s=e.from(typeof t=="number"?[t]:t,n),a=typeof r=="string"?0:r;return a=typeof r=="number"?a:null,a=Number.isNaN(a)?null:a,a??=i?this.length:0,a=a<0?this.length+a:a,s.length===0&&i===!1?a>=this.length?this.length:a:s.length===0&&i===!0?(a>=this.length?this.length:a)||this.length:o((f,T)=>(i?T<=a:T>=a)&&this[T]===s[0]&&s.every((A,R)=>this[T+R]===A))}toString(t="utf8",r=0,n=this.length){if(r=r<0?0:r,t=t.toString().toLowerCase(),n<=0)return"";if(t==="utf8"||t==="utf-8")return Qu.decode(this.slice(r,n));if(t==="base64"||t==="base64url"){let i=btoa(this.reduce((o,s)=>o+zn(s),""));return t==="base64url"?i.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):i}if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return this.slice(r,n).reduce((i,o)=>i+zn(o&(t==="ascii"?127:255)),"");if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let i=new DataView(this.buffer.slice(r,n));return Array.from({length:i.byteLength/2},(o,s)=>s*2+1<i.byteLength?zn(i.getUint16(s*2,!0)):"").join("")}if(t==="hex")return this.slice(r,n).reduce((i,o)=>i+o.toString(16).padStart(2,"0"),"");Co(`encoding "${t}"`)}toLocaleString(){return this.toString()}inspect(){return`<Buffer ${this.toString("hex").match(/.{1,2}/g).join(" ")}>`}};Bu={int8:[-128,127],int16:[-32768,32767],int32:[-2147483648,2147483647],uint8:[0,255],uint16:[0,65535],uint32:[0,4294967295],float32:[-1/0,1/0],float64:[-1/0,1/0],bigint64:[-0x8000000000000000n,0x7fffffffffffffffn],biguint64:[0n,0xffffffffffffffffn]},ju=new TextEncoder,Qu=new TextDecoder,Gu=["utf8","utf-8","hex","base64","ascii","binary","base64url","ucs2","ucs-2","utf16le","utf-16le","latin1","latin-1"],Hu=4294967295;Vu(h.prototype);y=new Proxy(Ju,{construct(e,[t,r]){return h.from(t,r)},get(e,t){return h[t]}}),zn=String.fromCodePoint});var g,c=de(()=>{"use strict";g={nextTick:(e,...t)=>{setTimeout(()=>{e(...t)},0)},env:{},version:"",cwd:()=>"/",stderr:{},argv:["/bin/node"],pid:1e4}});var w,p=de(()=>{"use strict";w=globalThis.performance??(()=>{let e=Date.now();return{now:()=>Date.now()-e}})()});var b,m=de(()=>{"use strict";b=()=>{};b.prototype=b});var d=de(()=>{"use strict"});function Oo(e,t){var r,n,i,o,s,a,f,T,v=e.constructor,A=v.precision;if(!e.s||!t.s)return t.s||(t=new v(e)),H?$(t,A):t;if(f=e.d,T=t.d,s=e.e,i=t.e,f=f.slice(),o=s-i,o){for(o<0?(n=f,o=-o,a=T.length):(n=T,i=s,a=f.length),s=Math.ceil(A/j),a=s>a?s+1:a+1,o>a&&(o=a,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for(a=f.length,o=T.length,a-o<0&&(o=a,n=T,T=f,f=n),r=0;o;)r=(f[--o]=f[o]+T[o]+r)/X|0,f[o]%=X;for(r&&(f.unshift(r),++i),a=f.length;f[--a]==0;)f.pop();return t.d=f,t.e=i,H?$(t,A):t}function Ce(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Ke+e)}function Ae(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=j-n.length,r&&(o+=$e(r)),o+=n;s=e[t],n=s+"",r=j-n.length,r&&(o+=$e(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function ko(e,t){var r,n,i,o,s,a,f=0,T=0,v=e.constructor,A=v.precision;if(z(e)>16)throw Error(Zn+z(e));if(!e.s)return new v(fe);for(t==null?(H=!1,a=A):a=t,s=new v(.03125);e.abs().gte(.1);)e=e.times(s),T+=5;for(n=Math.log(We(2,T))/Math.LN10*2+5|0,a+=n,r=i=o=new v(fe),v.precision=a;;){if(i=$(i.times(e),a),r=r.times(++f),s=o.plus(Me(i,r,a)),Ae(s.d).slice(0,a)===Ae(o.d).slice(0,a)){for(;T--;)o=$(o.times(o),a);return v.precision=A,t==null?(H=!0,$(o,A)):o}o=s}}function z(e){for(var t=e.e*j,r=e.d[0];r>=10;r/=10)t++;return t}function Yn(e,t,r){if(t>e.LN10.sd())throw H=!0,r&&(e.precision=r),Error(he+"LN10 precision limit exceeded");return $(new e(e.LN10),t)}function $e(e){for(var t="";e--;)t+="0";return t}function Bt(e,t){var r,n,i,o,s,a,f,T,v,A=1,R=10,C=e,D=C.d,I=C.constructor,M=I.precision;if(C.s<1)throw Error(he+(C.s?"NaN":"-Infinity"));if(C.eq(fe))return new I(0);if(t==null?(H=!1,T=M):T=t,C.eq(10))return t==null&&(H=!0),Yn(I,T);if(T+=R,I.precision=T,r=Ae(D),n=r.charAt(0),o=z(C),Math.abs(o)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)C=C.times(e),r=Ae(C.d),n=r.charAt(0),A++;o=z(C),n>1?(C=new I("0."+r),o++):C=new I(n+"."+r.slice(1))}else return f=Yn(I,T+2,M).times(o+""),C=Bt(new I(n+"."+r.slice(1)),T-R).plus(f),I.precision=M,t==null?(H=!0,$(C,M)):C;for(a=s=C=Me(C.minus(fe),C.plus(fe),T),v=$(C.times(C),T),i=3;;){if(s=$(s.times(v),T),f=a.plus(Me(s,new I(i),T)),Ae(f.d).slice(0,T)===Ae(a.d).slice(0,T))return a=a.times(2),o!==0&&(a=a.plus(Yn(I,T+2,M).times(o+""))),a=Me(a,new I(A),T),I.precision=M,t==null?(H=!0,$(a,M)):a;a=f,i+=2}}function Ro(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=mt(r/j),e.d=[],n=(r+1)%j,r<0&&(n+=j),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=j;n<i;)e.d.push(+t.slice(n,n+=j));t=t.slice(n),n=j-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),H&&(e.e>Qr||e.e<-Qr))throw Error(Zn+r)}else e.s=0,e.e=0,e.d=[0];return e}function $(e,t,r){var n,i,o,s,a,f,T,v,A=e.d;for(s=1,o=A[0];o>=10;o/=10)s++;if(n=t-s,n<0)n+=j,i=t,T=A[v=0];else{if(v=Math.ceil((n+1)/j),o=A.length,v>=o)return e;for(T=o=A[v],s=1;o>=10;o/=10)s++;n%=j,i=n-j+s}if(r!==void 0&&(o=We(10,s-i-1),a=T/o%10|0,f=t<0||A[v+1]!==void 0||T%o,f=r<4?(a||f)&&(r==0||r==(e.s<0?3:2)):a>5||a==5&&(r==4||f||r==6&&(n>0?i>0?T/We(10,s-i):0:A[v-1])%10&1||r==(e.s<0?8:7))),t<1||!A[0])return f?(o=z(e),A.length=1,t=t-o-1,A[0]=We(10,(j-t%j)%j),e.e=mt(-t/j)||0):(A.length=1,A[0]=e.e=e.s=0),e;if(n==0?(A.length=v,o=1,v--):(A.length=v+1,o=We(10,j-n),A[v]=i>0?(T/We(10,s-i)%We(10,i)|0)*o:0),f)for(;;)if(v==0){(A[0]+=o)==X&&(A[0]=1,++e.e);break}else{if(A[v]+=o,A[v]!=X)break;A[v--]=0,o=1}for(n=A.length;A[--n]===0;)A.pop();if(H&&(e.e>Qr||e.e<-Qr))throw Error(Zn+z(e));return e}function Do(e,t){var r,n,i,o,s,a,f,T,v,A,R=e.constructor,C=R.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new R(e),H?$(t,C):t;if(f=e.d,A=t.d,n=t.e,T=e.e,f=f.slice(),s=T-n,s){for(v=s<0,v?(r=f,s=-s,a=A.length):(r=A,n=T,a=f.length),i=Math.max(Math.ceil(C/j),a)+2,s>i&&(s=i,r.length=1),r.reverse(),i=s;i--;)r.push(0);r.reverse()}else{for(i=f.length,a=A.length,v=i<a,v&&(a=i),i=0;i<a;i++)if(f[i]!=A[i]){v=f[i]<A[i];break}s=0}for(v&&(r=f,f=A,A=r,t.s=-t.s),a=f.length,i=A.length-a;i>0;--i)f[a++]=0;for(i=A.length;i>s;){if(f[--i]<A[i]){for(o=i;o&&f[--o]===0;)f[o]=X-1;--f[o],f[i]+=X}f[i]-=A[i]}for(;f[--a]===0;)f.pop();for(;f[0]===0;f.shift())--n;return f[0]?(t.d=f,t.e=n,H?$(t,C):t):new R(0)}function ze(e,t,r){var n,i=z(e),o=Ae(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+$e(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+$e(-i-1)+o,r&&(n=r-s)>0&&(o+=$e(n))):i>=s?(o+=$e(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+$e(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=$e(n))),e.s<0?"-"+o:o}function So(e,t){if(e.length>t)return e.length=t,!0}function _o(e){var t,r,n;function i(o){var s=this;if(!(s instanceof i))return new i(o);if(s.constructor=i,o instanceof i){s.s=o.s,s.e=o.e,s.d=(o=o.d)?o.slice():o;return}if(typeof o=="number"){if(o*0!==0)throw Error(Ke+o);if(o>0)s.s=1;else if(o<0)o=-o,s.s=-1;else{s.s=0,s.e=0,s.d=[0];return}if(o===~~o&&o<1e7){s.e=0,s.d=[o];return}return Ro(s,o.toString())}else if(typeof o!="string")throw Error(Ke+o);if(o.charCodeAt(0)===45?(o=o.slice(1),s.s=-1):s.s=1,Ku.test(o))Ro(s,o);else throw Error(Ke+o)}if(i.prototype=S,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=_o,i.config=i.set=zu,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function zu(e){if(!e||typeof e!="object")throw Error(he+"Object expected");var t,r,n,i=["precision",1,pt,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(mt(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(Ke+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(Ke+r+": "+n);return this}var pt,Wu,Xn,H,he,Ke,Zn,mt,We,Ku,fe,X,j,Io,Qr,S,Me,Xn,Gr,Mo=de(()=>{"use strict";u();c();p();m();d();l();pt=1e9,Wu={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},H=!0,he="[DecimalError] ",Ke=he+"Invalid argument: ",Zn=he+"Exponent out of range: ",mt=Math.floor,We=Math.pow,Ku=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,X=1e7,j=7,Io=9007199254740991,Qr=mt(Io/j),S={};S.absoluteValue=S.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};S.comparedTo=S.cmp=function(e){var t,r,n,i,o=this;if(e=new o.constructor(e),o.s!==e.s)return o.s||-e.s;if(o.e!==e.e)return o.e>e.e^o.s<0?1:-1;for(n=o.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(o.d[t]!==e.d[t])return o.d[t]>e.d[t]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1};S.decimalPlaces=S.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*j;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};S.dividedBy=S.div=function(e){return Me(this,new this.constructor(e))};S.dividedToIntegerBy=S.idiv=function(e){var t=this,r=t.constructor;return $(Me(t,new r(e),0,1),r.precision)};S.equals=S.eq=function(e){return!this.cmp(e)};S.exponent=function(){return z(this)};S.greaterThan=S.gt=function(e){return this.cmp(e)>0};S.greaterThanOrEqualTo=S.gte=function(e){return this.cmp(e)>=0};S.isInteger=S.isint=function(){return this.e>this.d.length-2};S.isNegative=S.isneg=function(){return this.s<0};S.isPositive=S.ispos=function(){return this.s>0};S.isZero=function(){return this.s===0};S.lessThan=S.lt=function(e){return this.cmp(e)<0};S.lessThanOrEqualTo=S.lte=function(e){return this.cmp(e)<1};S.logarithm=S.log=function(e){var t,r=this,n=r.constructor,i=n.precision,o=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(fe))throw Error(he+"NaN");if(r.s<1)throw Error(he+(r.s?"NaN":"-Infinity"));return r.eq(fe)?new n(0):(H=!1,t=Me(Bt(r,o),Bt(e,o),o),H=!0,$(t,i))};S.minus=S.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Do(t,e):Oo(t,(e.s=-e.s,e))};S.modulo=S.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(he+"NaN");return r.s?(H=!1,t=Me(r,e,0,1).times(e),H=!0,r.minus(t)):$(new n(r),i)};S.naturalExponential=S.exp=function(){return ko(this)};S.naturalLogarithm=S.ln=function(){return Bt(this)};S.negated=S.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};S.plus=S.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Oo(t,e):Do(t,(e.s=-e.s,e))};S.precision=S.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Ke+e);if(t=z(i)+1,n=i.d.length-1,r=n*j+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};S.squareRoot=S.sqrt=function(){var e,t,r,n,i,o,s,a=this,f=a.constructor;if(a.s<1){if(!a.s)return new f(0);throw Error(he+"NaN")}for(e=z(a),H=!1,i=Math.sqrt(+a),i==0||i==1/0?(t=Ae(a.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=mt((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new f(t)):n=new f(i.toString()),r=f.precision,i=s=r+3;;)if(o=n,n=o.plus(Me(a,o,s+2)).times(.5),Ae(o.d).slice(0,s)===(t=Ae(n.d)).slice(0,s)){if(t=t.slice(s-3,s+1),i==s&&t=="4999"){if($(o,r+1,0),o.times(o).eq(a)){n=o;break}}else if(t!="9999")break;s+=4}return H=!0,$(n,r)};S.times=S.mul=function(e){var t,r,n,i,o,s,a,f,T,v=this,A=v.constructor,R=v.d,C=(e=new A(e)).d;if(!v.s||!e.s)return new A(0);for(e.s*=v.s,r=v.e+e.e,f=R.length,T=C.length,f<T&&(o=R,R=C,C=o,s=f,f=T,T=s),o=[],s=f+T,n=s;n--;)o.push(0);for(n=T;--n>=0;){for(t=0,i=f+n;i>n;)a=o[i]+C[n]*R[i-n-1]+t,o[i--]=a%X|0,t=a/X|0;o[i]=(o[i]+t)%X|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,H?$(e,A.precision):e};S.toDecimalPlaces=S.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(Ce(e,0,pt),t===void 0?t=n.rounding:Ce(t,0,8),$(r,e+z(r)+1,t))};S.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=ze(n,!0):(Ce(e,0,pt),t===void 0?t=i.rounding:Ce(t,0,8),n=$(new i(n),e+1,t),r=ze(n,!0,e+1)),r};S.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?ze(i):(Ce(e,0,pt),t===void 0?t=o.rounding:Ce(t,0,8),n=$(new o(i),e+z(i)+1,t),r=ze(n.abs(),!1,e+z(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};S.toInteger=S.toint=function(){var e=this,t=e.constructor;return $(new t(e),z(e)+1,t.rounding)};S.toNumber=function(){return+this};S.toPower=S.pow=function(e){var t,r,n,i,o,s,a=this,f=a.constructor,T=12,v=+(e=new f(e));if(!e.s)return new f(fe);if(a=new f(a),!a.s){if(e.s<1)throw Error(he+"Infinity");return a}if(a.eq(fe))return a;if(n=f.precision,e.eq(fe))return $(a,n);if(t=e.e,r=e.d.length-1,s=t>=r,o=a.s,s){if((r=v<0?-v:v)<=Io){for(i=new f(fe),t=Math.ceil(n/j+4),H=!1;r%2&&(i=i.times(a),So(i.d,t)),r=mt(r/2),r!==0;)a=a.times(a),So(a.d,t);return H=!0,e.s<0?new f(fe).div(i):$(i,n)}}else if(o<0)throw Error(he+"NaN");return o=o<0&&e.d[Math.max(t,r)]&1?-1:1,a.s=1,H=!1,i=e.times(Bt(a,n+T)),H=!0,i=ko(i),i.s=o,i};S.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?(r=z(i),n=ze(i,r<=o.toExpNeg||r>=o.toExpPos)):(Ce(e,1,pt),t===void 0?t=o.rounding:Ce(t,0,8),i=$(new o(i),e,t),r=z(i),n=ze(i,e<=r||r<=o.toExpNeg,e)),n};S.toSignificantDigits=S.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(Ce(e,1,pt),t===void 0?t=n.rounding:Ce(t,0,8)),$(new n(r),e,t)};S.toString=S.valueOf=S.val=S.toJSON=S[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=z(e),r=e.constructor;return ze(e,t<=r.toExpNeg||t>=r.toExpPos)};Me=function(){function e(n,i){var o,s=0,a=n.length;for(n=n.slice();a--;)o=n[a]*i+s,n[a]=o%X|0,s=o/X|0;return s&&n.unshift(s),n}function t(n,i,o,s){var a,f;if(o!=s)f=o>s?1:-1;else for(a=f=0;a<o;a++)if(n[a]!=i[a]){f=n[a]>i[a]?1:-1;break}return f}function r(n,i,o){for(var s=0;o--;)n[o]-=s,s=n[o]<i[o]?1:0,n[o]=s*X+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s){var a,f,T,v,A,R,C,D,I,M,be,le,V,ue,Je,Wn,Ee,Vr,qr=n.constructor,_u=n.s==i.s?1:-1,ve=n.d,W=i.d;if(!n.s)return new qr(n);if(!i.s)throw Error(he+"Division by zero");for(f=n.e-i.e,Ee=W.length,Je=ve.length,C=new qr(_u),D=C.d=[],T=0;W[T]==(ve[T]||0);)++T;if(W[T]>(ve[T]||0)&&--f,o==null?le=o=qr.precision:s?le=o+(z(n)-z(i))+1:le=o,le<0)return new qr(0);if(le=le/j+2|0,T=0,Ee==1)for(v=0,W=W[0],le++;(T<Je||v)&&le--;T++)V=v*X+(ve[T]||0),D[T]=V/W|0,v=V%W|0;else{for(v=X/(W[0]+1)|0,v>1&&(W=e(W,v),ve=e(ve,v),Ee=W.length,Je=ve.length),ue=Ee,I=ve.slice(0,Ee),M=I.length;M<Ee;)I[M++]=0;Vr=W.slice(),Vr.unshift(0),Wn=W[0],W[1]>=X/2&&++Wn;do v=0,a=t(W,I,Ee,M),a<0?(be=I[0],Ee!=M&&(be=be*X+(I[1]||0)),v=be/Wn|0,v>1?(v>=X&&(v=X-1),A=e(W,v),R=A.length,M=I.length,a=t(A,I,R,M),a==1&&(v--,r(A,Ee<R?Vr:W,R))):(v==0&&(a=v=1),A=W.slice()),R=A.length,R<M&&A.unshift(0),r(I,A,M),a==-1&&(M=I.length,a=t(W,I,Ee,M),a<1&&(v++,r(I,Ee<M?Vr:W,M))),M=I.length):a===0&&(v++,I=[0]),D[T++]=v,a&&I[0]?I[M++]=ve[ue]||0:(I=[ve[ue]],M=1);while((ue++<Je||I[0]!==void 0)&&le--)}return D[0]||D.shift(),C.e=f,$(C,s?o+z(C)+1:o)}}();Xn=_o(Wu);fe=new Xn(1);Gr=Xn});var P,ne,l=de(()=>{"use strict";Mo();P=class extends Gr{static isDecimal(t){return t instanceof Gr}static random(t=20){{let n=globalThis.crypto.getRandomValues(new Uint8Array(t)).reduce((i,o)=>i+o,"");return new Gr(`0.${n.slice(0,t)}`)}}},ne=P});function rc(){return!1}function Xo(){return{dev:0,ino:0,mode:0,nlink:0,uid:0,gid:0,rdev:0,size:0,blksize:0,blocks:0,atimeMs:0,mtimeMs:0,ctimeMs:0,birthtimeMs:0,atime:new Date,mtime:new Date,ctime:new Date,birthtime:new Date}}function nc(){return Xo()}function ic(){return[]}function oc(e){e(null,[])}function sc(){return""}function ac(){return""}function lc(){}function uc(){}function cc(){}function pc(){}function mc(){}function dc(){}var fc,gc,es,ts=de(()=>{"use strict";u();c();p();m();d();l();fc={},gc={existsSync:rc,lstatSync:Xo,statSync:nc,readdirSync:ic,readdir:oc,readlinkSync:sc,realpathSync:ac,chmodSync:lc,renameSync:uc,mkdirSync:cc,rmdirSync:pc,rmSync:mc,unlinkSync:dc,promises:fc},es=gc});var rs=oe(()=>{"use strict";u();c();p();m();d();l()});function yc(...e){return e.join("/")}function hc(...e){return e.join("/")}function wc(e){let t=ns(e),r=is(e),[n,i]=t.split(".");return{root:"/",dir:r,base:t,ext:i,name:n}}function ns(e){let t=e.split("/");return t[t.length-1]}function is(e){return e.split("/").slice(0,-1).join("/")}var os,bc,Ec,Kr,ss=de(()=>{"use strict";u();c();p();m();d();l();os="/",bc={sep:os},Ec={basename:ns,dirname:is,join:hc,parse:wc,posix:bc,resolve:yc,sep:os},Kr=Ec});var as=oe((my,xc)=>{xc.exports={name:"@prisma/internals",version:"6.10.1",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",esbuild:"0.25.1","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-ansi":"6.0.1","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c","@prisma/schema-engine-wasm":"6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var ni={};qt(ni,{Hash:()=>Gt,createHash:()=>ls,default:()=>gt,randomFillSync:()=>Yr,randomUUID:()=>zr,webcrypto:()=>Ht});function zr(){return globalThis.crypto.randomUUID()}function Yr(e,t,r){return t!==void 0&&(r!==void 0?e=e.subarray(t,t+r):e=e.subarray(t)),globalThis.crypto.getRandomValues(e)}function ls(e){return new Gt(e)}var Ht,Gt,gt,Ye=de(()=>{"use strict";u();c();p();m();d();l();Ht=globalThis.crypto;Gt=class{#e=[];#r;constructor(t){this.#r=t}update(t){this.#e.push(t)}async digest(){let t=new Uint8Array(this.#e.reduce((i,o)=>i+o.length,0)),r=0;for(let i of this.#e)t.set(i,r),r+=i.length;let n=await globalThis.crypto.subtle.digest(this.#r,t);return new Uint8Array(n)}},gt={webcrypto:Ht,randomUUID:zr,randomFillSync:Yr,createHash:ls,Hash:Gt}});var ps=oe((Vy,cs)=>{"use strict";u();c();p();m();d();l();cs.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var fs=oe((eh,ds)=>{"use strict";u();c();p();m();d();l();ds.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var si=oe((ah,gs)=>{"use strict";u();c();p();m();d();l();var Ic=fs();gs.exports=e=>typeof e=="string"?e.replace(Ic(),""):e});var ys=oe((xh,en)=>{"use strict";u();c();p();m();d();l();en.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};en.exports.default=en.exports});var ci=oe((Px,Es)=>{"use strict";u();c();p();m();d();l();Es.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,f,T,v,A,R,C,D,I,M,be,le,V,ue=[];for(f=0;f<i;f++)ue.push(f+1),ue.push(t.charCodeAt(s+f));for(var Je=ue.length-1;a<o-3;)for(M=r.charCodeAt(s+(T=a)),be=r.charCodeAt(s+(v=a+1)),le=r.charCodeAt(s+(A=a+2)),V=r.charCodeAt(s+(R=a+3)),C=a+=4,f=0;f<Je;f+=2)D=ue[f],I=ue[f+1],T=e(D,T,v,M,I),v=e(T,v,A,be,I),A=e(v,A,R,le,I),C=e(A,R,C,V,I),ue[f]=C,R=A,A=v,v=T,T=D;for(;a<o;)for(M=r.charCodeAt(s+(T=a)),C=++a,f=0;f<Je;f+=2)D=ue[f],ue[f]=C=e(D,T,C,M,ue[f+1]),T=D;return C}}()});var As=de(()=>{"use strict";u();c();p();m();d();l()});var Cs=de(()=>{"use strict";u();c();p();m();d();l()});var Js=oe((xC,Cp)=>{Cp.exports={name:"@prisma/engines-version",version:"6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"9b628578b3b7cae625e8c927178f15a170e74a9c"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var bn,Ws=de(()=>{"use strict";u();c();p();m();d();l();bn=class{events={};on(t,r){return this.events[t]||(this.events[t]=[]),this.events[t].push(r),this}emit(t,...r){return this.events[t]?(this.events[t].forEach(n=>{n(...r)}),!0):!1}}});var Li=oe(rt=>{"use strict";u();c();p();m();d();l();Object.defineProperty(rt,"__esModule",{value:!0});rt.anumber=Ni;rt.abytes=$a;rt.ahash=hm;rt.aexists=wm;rt.aoutput=bm;function Ni(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function ym(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function $a(e,...t){if(!ym(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function hm(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Ni(e.outputLen),Ni(e.blockLen)}function wm(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function bm(e,t){$a(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var al=oe(k=>{"use strict";u();c();p();m();d();l();Object.defineProperty(k,"__esModule",{value:!0});k.add5L=k.add5H=k.add4H=k.add4L=k.add3H=k.add3L=k.rotlBL=k.rotlBH=k.rotlSL=k.rotlSH=k.rotr32L=k.rotr32H=k.rotrBL=k.rotrBH=k.rotrSL=k.rotrSH=k.shrSL=k.shrSH=k.toBig=void 0;k.fromBig=Fi;k.split=Va;k.add=el;var Rn=BigInt(2**32-1),Ui=BigInt(32);function Fi(e,t=!1){return t?{h:Number(e&Rn),l:Number(e>>Ui&Rn)}:{h:Number(e>>Ui&Rn)|0,l:Number(e&Rn)|0}}function Va(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=Fi(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var qa=(e,t)=>BigInt(e>>>0)<<Ui|BigInt(t>>>0);k.toBig=qa;var Ba=(e,t,r)=>e>>>r;k.shrSH=Ba;var ja=(e,t,r)=>e<<32-r|t>>>r;k.shrSL=ja;var Qa=(e,t,r)=>e>>>r|t<<32-r;k.rotrSH=Qa;var Ga=(e,t,r)=>e<<32-r|t>>>r;k.rotrSL=Ga;var Ha=(e,t,r)=>e<<64-r|t>>>r-32;k.rotrBH=Ha;var Ja=(e,t,r)=>e>>>r-32|t<<64-r;k.rotrBL=Ja;var Wa=(e,t)=>t;k.rotr32H=Wa;var Ka=(e,t)=>e;k.rotr32L=Ka;var za=(e,t,r)=>e<<r|t>>>32-r;k.rotlSH=za;var Ya=(e,t,r)=>t<<r|e>>>32-r;k.rotlSL=Ya;var Za=(e,t,r)=>t<<r-32|e>>>64-r;k.rotlBH=Za;var Xa=(e,t,r)=>e<<r-32|t>>>64-r;k.rotlBL=Xa;function el(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var tl=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);k.add3L=tl;var rl=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;k.add3H=rl;var nl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);k.add4L=nl;var il=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;k.add4H=il;var ol=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);k.add5L=ol;var sl=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;k.add5H=sl;var Em={fromBig:Fi,split:Va,toBig:qa,shrSH:Ba,shrSL:ja,rotrSH:Qa,rotrSL:Ga,rotrBH:Ha,rotrBL:Ja,rotr32H:Wa,rotr32L:Ka,rotlSH:za,rotlSL:Ya,rotlBH:Za,rotlBL:Xa,add:el,add3L:tl,add3H:rl,add4L:nl,add4H:il,add5H:sl,add5L:ol};k.default=Em});var ll=oe(Sn=>{"use strict";u();c();p();m();d();l();Object.defineProperty(Sn,"__esModule",{value:!0});Sn.crypto=void 0;var je=(Ye(),$u(ni));Sn.crypto=je&&typeof je=="object"&&"webcrypto"in je?je.webcrypto:je&&typeof je=="object"&&"randomBytes"in je?je:void 0});var pl=oe(L=>{"use strict";u();c();p();m();d();l();Object.defineProperty(L,"__esModule",{value:!0});L.Hash=L.nextTick=L.byteSwapIfBE=L.isLE=void 0;L.isBytes=xm;L.u8=Pm;L.u32=Tm;L.createView=vm;L.rotr=Am;L.rotl=Cm;L.byteSwap=qi;L.byteSwap32=Rm;L.bytesToHex=Im;L.hexToBytes=Om;L.asyncLoop=Dm;L.utf8ToBytes=cl;L.toBytes=In;L.concatBytes=_m;L.checkOpts=Mm;L.wrapConstructor=Nm;L.wrapConstructorWithOpts=Lm;L.wrapXOFConstructorWithOpts=Um;L.randomBytes=Fm;var It=ll(),Vi=Li();function xm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Pm(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function Tm(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function vm(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Am(e,t){return e<<32-t|e>>>t}function Cm(e,t){return e<<t|e>>>32-t>>>0}L.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function qi(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}L.byteSwapIfBE=L.isLE?e=>e:e=>qi(e);function Rm(e){for(let t=0;t<e.length;t++)e[t]=qi(e[t])}var Sm=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function Im(e){(0,Vi.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=Sm[e[r]];return t}var Le={_0:48,_9:57,A:65,F:70,a:97,f:102};function ul(e){if(e>=Le._0&&e<=Le._9)return e-Le._0;if(e>=Le.A&&e<=Le.F)return e-(Le.A-10);if(e>=Le.a&&e<=Le.f)return e-(Le.a-10)}function Om(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=ul(e.charCodeAt(o)),a=ul(e.charCodeAt(o+1));if(s===void 0||a===void 0){let f=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+f+'" at index '+o)}n[i]=s*16+a}return n}var km=async()=>{};L.nextTick=km;async function Dm(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,L.nextTick)(),n+=o)}}function cl(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function In(e){return typeof e=="string"&&(e=cl(e)),(0,Vi.abytes)(e),e}function _m(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,Vi.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var $i=class{clone(){return this._cloneInto()}};L.Hash=$i;function Mm(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function Nm(e){let t=n=>e().update(In(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function Lm(e){let t=(n,i)=>e(i).update(In(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Um(e){let t=(n,i)=>e(i).update(In(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Fm(e=32){if(It.crypto&&typeof It.crypto.getRandomValues=="function")return It.crypto.getRandomValues(new Uint8Array(e));if(It.crypto&&typeof It.crypto.randomBytes=="function")return It.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var bl=oe(G=>{"use strict";u();c();p();m();d();l();Object.defineProperty(G,"__esModule",{value:!0});G.shake256=G.shake128=G.keccak_512=G.keccak_384=G.keccak_256=G.keccak_224=G.sha3_512=G.sha3_384=G.sha3_256=G.sha3_224=G.Keccak=void 0;G.keccakP=hl;var Ot=Li(),yr=al(),Ue=pl(),fl=[],gl=[],yl=[],$m=BigInt(0),gr=BigInt(1),Vm=BigInt(2),qm=BigInt(7),Bm=BigInt(256),jm=BigInt(113);for(let e=0,t=gr,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],fl.push(2*(5*n+r)),gl.push((e+1)*(e+2)/2%64);let i=$m;for(let o=0;o<7;o++)t=(t<<gr^(t>>qm)*jm)%Bm,t&Vm&&(i^=gr<<(gr<<BigInt(o))-gr);yl.push(i)}var[Qm,Gm]=(0,yr.split)(yl,!0),ml=(e,t,r)=>r>32?(0,yr.rotlBH)(e,t,r):(0,yr.rotlSH)(e,t,r),dl=(e,t,r)=>r>32?(0,yr.rotlBL)(e,t,r):(0,yr.rotlSL)(e,t,r);function hl(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,f=(s+2)%10,T=r[f],v=r[f+1],A=ml(T,v,1)^r[a],R=dl(T,v,1)^r[a+1];for(let C=0;C<50;C+=10)e[s+C]^=A,e[s+C+1]^=R}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=gl[s],f=ml(i,o,a),T=dl(i,o,a),v=fl[s];i=e[v],o=e[v+1],e[v]=f,e[v+1]=T}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=Qm[n],e[1]^=Gm[n]}r.fill(0)}var hr=class e extends Ue.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,Ot.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Ue.u32)(this.state)}keccak(){Ue.isLE||(0,Ue.byteSwap32)(this.state32),hl(this.state32,this.rounds),Ue.isLE||(0,Ue.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,Ot.aexists)(this);let{blockLen:r,state:n}=this;t=(0,Ue.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,Ot.aexists)(this,!1),(0,Ot.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,Ot.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,Ot.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};G.Keccak=hr;var Qe=(e,t,r)=>(0,Ue.wrapConstructor)(()=>new hr(t,e,r));G.sha3_224=Qe(6,144,224/8);G.sha3_256=Qe(6,136,256/8);G.sha3_384=Qe(6,104,384/8);G.sha3_512=Qe(6,72,512/8);G.keccak_224=Qe(1,144,224/8);G.keccak_256=Qe(1,136,256/8);G.keccak_384=Qe(1,104,384/8);G.keccak_512=Qe(1,72,512/8);var wl=(e,t,r)=>(0,Ue.wrapXOFConstructorWithOpts)((n={})=>new hr(t,e,n.dkLen===void 0?r:n.dkLen,!0));G.shake128=wl(31,168,128/8);G.shake256=wl(31,136,256/8)});var Rl=oe((k1,Ge)=>{"use strict";u();c();p();m();d();l();var{sha3_512:Hm}=bl(),xl=24,wr=32,Bi=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function Pl(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var Tl=(e="")=>Pl(Hm(e)).toString(36).slice(1),El=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),Jm=e=>El[Math.floor(e()*El.length)],vl=({globalObj:e=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+Bi(wr,t):Bi(wr,t);return Tl(n).substring(0,wr)},Al=e=>()=>e++,Wm=476782367,Cl=({random:e=Math.random,counter:t=Al(Math.floor(e()*Wm)),length:r=xl,fingerprint:n=vl({random:e})}={})=>function(){let o=Jm(e),s=Date.now().toString(36),a=t().toString(36),f=Bi(r,e),T=`${s+f+a+n}`;return`${o+Tl(T).substring(1,r)}`},Km=Cl(),zm=(e,{minLength:t=2,maxLength:r=wr}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};Ge.exports.getConstants=()=>({defaultLength:xl,bigLength:wr});Ge.exports.init=Cl;Ge.exports.createId=Km;Ge.exports.bufToBigInt=Pl;Ge.exports.createCounter=Al;Ge.exports.createFingerprint=vl;Ge.exports.isCuid=zm});var Sl=oe((F1,br)=>{"use strict";u();c();p();m();d();l();var{createId:Ym,init:Zm,getConstants:Xm,isCuid:ed}=Rl();br.exports.createId=Ym;br.exports.init=Zm;br.exports.getConstants=Xm;br.exports.isCuid=ed});u();c();p();m();d();l();var Uo={};qt(Uo,{defineExtension:()=>No,getExtensionContext:()=>Lo});u();c();p();m();d();l();u();c();p();m();d();l();function No(e){return typeof e=="function"?e:t=>t.$extends(e)}u();c();p();m();d();l();function Lo(e){return e}var $o={};qt($o,{validator:()=>Fo});u();c();p();m();d();l();u();c();p();m();d();l();function Fo(...e){return t=>t}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var ei,Vo,qo,Bo,jo=!0;typeof g<"u"&&({FORCE_COLOR:ei,NODE_DISABLE_COLORS:Vo,NO_COLOR:qo,TERM:Bo}=g.env||{},jo=g.stdout&&g.stdout.isTTY);var Yu={enabled:!Vo&&qo==null&&Bo!=="dumb"&&(ei!=null&&ei!=="0"||jo)};function q(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!Yu.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var rg=q(0,0),Hr=q(1,22),Jr=q(2,22),ng=q(3,23),Wr=q(4,24),ig=q(7,27),og=q(8,28),sg=q(9,29),ag=q(30,39),dt=q(31,39),Qo=q(32,39),Go=q(33,39),Ho=q(34,39),lg=q(35,39),Jo=q(36,39),ug=q(37,39),Wo=q(90,39),cg=q(90,39),pg=q(40,49),mg=q(41,49),dg=q(42,49),fg=q(43,49),gg=q(44,49),yg=q(45,49),hg=q(46,49),wg=q(47,49);u();c();p();m();d();l();var Zu=100,Ko=["green","yellow","blue","magenta","cyan","red"],jt=[],zo=Date.now(),Xu=0,ti=typeof g<"u"?g.env:{};globalThis.DEBUG??=ti.DEBUG??"";globalThis.DEBUG_COLORS??=ti.DEBUG_COLORS?ti.DEBUG_COLORS==="true":!0;var Qt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function ec(e){let t={color:Ko[Xu++%Ko.length],enabled:Qt.enabled(e),namespace:e,log:Qt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&jt.push([o,...n]),jt.length>Zu&&jt.shift(),Qt.enabled(o)||i){let f=n.map(v=>typeof v=="string"?v:tc(v)),T=`+${Date.now()-zo}ms`;zo=Date.now(),a(o,...f,T)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var Y=new Proxy(ec,{get:(e,t)=>Qt[t],set:(e,t,r)=>Qt[t]=r});function tc(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function Yo(e=7500){let t=jt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function Zo(){jt.length=0}u();c();p();m();d();l();u();c();p();m();d();l();var Pc=as(),ri=Pc.version;u();c();p();m();d();l();function ft(e){let t=Tc();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":vc(e))}function Tc(){let e=g.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function vc(e){return e?.previewFeatures.includes("queryCompiler")?"client":"library"}u();c();p();m();d();l();var us="prisma+postgres",Zr=`${us}:`;function Xr(e){return e?.toString().startsWith(`${Zr}//`)??!1}function ii(e){if(!Xr(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")}var Wt={};qt(Wt,{error:()=>Rc,info:()=>Cc,log:()=>Ac,query:()=>Sc,should:()=>ms,tags:()=>Jt,warn:()=>oi});u();c();p();m();d();l();var Jt={error:dt("prisma:error"),warn:Go("prisma:warn"),info:Jo("prisma:info"),query:Ho("prisma:query")},ms={warn:()=>!g.env.PRISMA_DISABLE_WARNINGS};function Ac(...e){console.log(...e)}function oi(e,...t){ms.warn()&&console.warn(`${Jt.warn} ${e}`,...t)}function Cc(e,...t){console.info(`${Jt.info} ${e}`,...t)}function Rc(e,...t){console.error(`${Jt.error} ${e}`,...t)}function Sc(e,...t){console.log(`${Jt.query} ${e}`,...t)}u();c();p();m();d();l();function xe(e,t){throw new Error(t)}u();c();p();m();d();l();function ai(e,t){return Object.prototype.hasOwnProperty.call(e,t)}u();c();p();m();d();l();function yt(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}u();c();p();m();d();l();function li(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}u();c();p();m();d();l();function O(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}u();c();p();m();d();l();var hs=new Set,tn=(e,t,...r)=>{hs.has(e)||(hs.add(e),oi(t,...r))};var U=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};O(U,"PrismaClientInitializationError");u();c();p();m();d();l();var ee=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};O(ee,"PrismaClientKnownRequestError");u();c();p();m();d();l();var pe=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};O(pe,"PrismaClientRustPanicError");u();c();p();m();d();l();var se=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};O(se,"PrismaClientUnknownRequestError");u();c();p();m();d();l();var ie=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};O(ie,"PrismaClientValidationError");u();c();p();m();d();l();l();function Ze(e){return e===null?e:Array.isArray(e)?e.map(Ze):typeof e=="object"?Oc(e)?kc(e):e.constructor!==null&&e.constructor.name!=="Object"?e:yt(e,Ze):e}function Oc(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function kc({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new ne(t);case"Json":return JSON.parse(t);default:xe(t,"Unknown tagged value")}}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var Re=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};u();c();p();m();d();l();function Ve(e){return e.substring(0,1).toLowerCase()+e.substring(1)}u();c();p();m();d();l();function bs(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}u();c();p();m();d();l();function Kt(e){let t;return{get(){return t||(t={value:e()}),t.value}}}u();c();p();m();d();l();function Dc(e){return{models:ui(e.models),enums:ui(e.enums),types:ui(e.types)}}function ui(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}u();c();p();m();d();l();function ht(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function rn(e){return e.toString()!=="Invalid Date"}u();c();p();m();d();l();l();function wt(e){return P.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}u();c();p();m();d();l();u();c();p();m();d();l();var nn={};qt(nn,{ModelAction:()=>zt,datamodelEnumToSchemaEnum:()=>_c});u();c();p();m();d();l();u();c();p();m();d();l();function _c(e){return{name:e.name,values:e.values.map(t=>t.name)}}u();c();p();m();d();l();var zt=(V=>(V.findUnique="findUnique",V.findUniqueOrThrow="findUniqueOrThrow",V.findFirst="findFirst",V.findFirstOrThrow="findFirstOrThrow",V.findMany="findMany",V.create="create",V.createMany="createMany",V.createManyAndReturn="createManyAndReturn",V.update="update",V.updateMany="updateMany",V.updateManyAndReturn="updateManyAndReturn",V.upsert="upsert",V.delete="delete",V.deleteMany="deleteMany",V.groupBy="groupBy",V.count="count",V.aggregate="aggregate",V.findRaw="findRaw",V.aggregateRaw="aggregateRaw",V))(zt||{});var Mc=_e(ps());var Nc={red:dt,gray:Wo,dim:Jr,bold:Hr,underline:Wr,highlightSource:e=>e.highlight()},Lc={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function Uc({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function Fc({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],f=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${f}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${f}`)),t&&a.push(s.underline($c(t))),i){a.push("");let T=[i.toString()];o&&(T.push(o),T.push(s.dim(")"))),a.push(T.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function $c(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function on(e){let t=e.showColors?Nc:Lc,r;return typeof $getTemplateParameters<"u"?r=$getTemplateParameters(e,t):r=Uc(e),Fc(r,t)}u();c();p();m();d();l();var Ss=_e(ci());u();c();p();m();d();l();function Ts(e,t,r){let n=vs(e),i=Vc(n),o=Bc(i);o?sn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function vs(e){return e.errors.flatMap(t=>t.kind==="Union"?vs(t):[t])}function Vc(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:qc(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function qc(e,t){return[...new Set(e.concat(t))]}function Bc(e){return li(e,(t,r)=>{let n=xs(t),i=xs(r);return n!==i?n-i:Ps(t)-Ps(r)})}function xs(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function Ps(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}u();c();p();m();d();l();var ge=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};u();c();p();m();d();l();u();c();p();m();d();l();Cs();u();c();p();m();d();l();var bt=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};As();u();c();p();m();d();l();u();c();p();m();d();l();var an=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};u();c();p();m();d();l();var ln=e=>e,un={bold:ln,red:ln,green:ln,dim:ln,enabled:!1},Rs={bold:Hr,red:dt,green:Qo,dim:Jr,enabled:!0},Et={write(e){e.writeLine(",")}};u();c();p();m();d();l();var Se=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};u();c();p();m();d();l();var qe=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var xt=class extends qe{items=[];addItem(t){return this.items.push(new an(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new Se("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(Et,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var Pt=class e extends qe{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof xt&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new Se("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(Et,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};u();c();p();m();d();l();var te=class extends qe{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new Se(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};u();c();p();m();d();l();var Yt=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(Et,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function sn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":jc(e,t);break;case"IncludeOnScalar":Qc(e,t);break;case"EmptySelection":Gc(e,t,r);break;case"UnknownSelectionField":Kc(e,t);break;case"InvalidSelectionValue":zc(e,t);break;case"UnknownArgument":Yc(e,t);break;case"UnknownInputField":Zc(e,t);break;case"RequiredArgumentMissing":Xc(e,t);break;case"InvalidArgumentType":ep(e,t);break;case"InvalidArgumentValue":tp(e,t);break;case"ValueTooLarge":rp(e,t);break;case"SomeFieldsMissing":np(e,t);break;case"TooManyFieldsGiven":ip(e,t);break;case"Union":Ts(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function jc(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function Qc(e,t){let[r,n]=Zt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new ge(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${Xt(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function Gc(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){Hc(e,t,i);return}if(n.hasField("select")){Jc(e,t);return}}if(r?.[Ve(e.outputType.name)]){Wc(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function Hc(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new ge(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function Jc(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),ks(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${Xt(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function Wc(e,t){let r=new Yt;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new ge("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=Zt(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let f=a?.value.asObject()??new Pt;f.addSuggestion(n),a.value=f}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function Kc(e,t){let r=Ds(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":ks(n,e.outputType);break;case"include":op(n,e.outputType);break;case"omit":sp(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(Xt(n)),i.join(" ")})}function zc(e,t){let r=Ds(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function Yc(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),ap(n,e.arguments)),t.addErrorMessage(i=>Is(i,r,e.arguments.map(o=>o.name)))}function Zc(e,t){let[r,n]=Zt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&_s(o,e.inputType)}t.addErrorMessage(o=>Is(o,n,e.inputType.fields.map(s=>s.name)))}function Is(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=up(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(Xt(e)),n.join(" ")}function Xc(e,t){let r;t.addErrorMessage(f=>r?.value instanceof te&&r.value.text==="null"?`Argument \`${f.green(o)}\` must not be ${f.red("null")}.`:`Argument \`${f.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=Zt(e.argumentPath),s=new Yt,a=n.getDeepFieldValue(i)?.asObject();if(a)if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let f of e.inputTypes[0].fields)s.addField(f.name,f.typeNames.join(" | "));a.addSuggestion(new ge(o,s).makeRequired())}else{let f=e.inputTypes.map(Os).join(" | ");a.addSuggestion(new ge(o,f).makeRequired())}}function Os(e){return e.kind==="list"?`${Os(e.elementType)}[]`:e.name}function ep(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=cn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function tp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=cn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function rp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof te&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function np(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&_s(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${cn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(Xt(i)),o.join(" ")})}function ip(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${cn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function ks(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ge(r.name,"true"))}function op(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new ge(r.name,"true"))}function sp(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new ge(r.name,"true"))}function ap(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new ge(r.name,r.typeNames.join(" | ")))}function Ds(e,t){let[r,n]=Zt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),f=o?.getField(n);return o&&f?{parentKind:"select",parent:o,field:f,fieldName:n}:(f=s?.getField(n),s&&f?{parentKind:"include",field:f,parent:s,fieldName:n}:(f=a?.getField(n),a&&f?{parentKind:"omit",field:f,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function _s(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new ge(r.name,r.typeNames.join(" | ")))}function Zt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function Xt({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function cn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var lp=3;function up(e,t){let r=1/0,n;for(let i of t){let o=(0,Ss.default)(e,i);o>lp||o<r&&(r=o,n=i)}return n}u();c();p();m();d();l();u();c();p();m();d();l();var er=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function Tt(e){return e instanceof er}u();c();p();m();d();l();var pn=Symbol(),mi=new WeakMap,Ne=class{constructor(t){t===pn?mi.set(this,`Prisma.${this._getName()}`):mi.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return mi.get(this)}},tr=class extends Ne{_getNamespace(){return"NullTypes"}},rr=class extends tr{#e};fi(rr,"DbNull");var nr=class extends tr{#e};fi(nr,"JsonNull");var ir=class extends tr{#e};fi(ir,"AnyNull");var di={classes:{DbNull:rr,JsonNull:nr,AnyNull:ir},instances:{DbNull:new rr(pn),JsonNull:new nr(pn),AnyNull:new ir(pn)}};function fi(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}u();c();p();m();d();l();var Ms=": ",mn=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Ms.length}write(t){let r=new Se(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(Ms).write(this.value)}};var gi=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function vt(e){return new gi(Ns(e))}function Ns(e){let t=new Pt;for(let[r,n]of Object.entries(e)){let i=new mn(r,Ls(n));t.addField(i)}return t}function Ls(e){if(typeof e=="string")return new te(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new te(String(e));if(typeof e=="bigint")return new te(`${e}n`);if(e===null)return new te("null");if(e===void 0)return new te("undefined");if(wt(e))return new te(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return y.isBuffer(e)?new te(`Buffer.alloc(${e.byteLength})`):new te(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=rn(e)?e.toISOString():"Invalid Date";return new te(`new Date("${t}")`)}return e instanceof Ne?new te(`Prisma.${e._getName()}`):Tt(e)?new te(`prisma.${Ve(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?cp(e):typeof e=="object"?Ns(e):new te(Object.prototype.toString.call(e))}function cp(e){let t=new xt;for(let r of e)t.addItem(Ls(r));return t}function dn(e,t){let r=t==="pretty"?Rs:un,n=e.renderAllMessages(r),i=new bt(0,{colors:r}).write(e).toString();return{message:n,args:i}}function fn({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=vt(e);for(let A of t)sn(A,a,s);let{message:f,args:T}=dn(a,r),v=on({message:f,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:T});throw new ie(v,{clientVersion:o})}u();c();p();m();d();l();u();c();p();m();d();l();function Ie(e){return e.replace(/^./,t=>t.toLowerCase())}u();c();p();m();d();l();function Fs(e,t,r){let n=Ie(r);return!t.result||!(t.result.$allModels||t.result[n])?e:pp({...e,...Us(t.name,e,t.result.$allModels),...Us(t.name,e,t.result[n])})}function pp(e){let t=new Re,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return yt(e,n=>({...n,needs:r(n.name,new Set)}))}function Us(e,t,r){return r?yt(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:mp(t,o,i)})):{}}function mp(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function $s(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function Vs(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var gn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new Re;modelExtensionsCache=new Re;queryCallbacksCache=new Re;clientExtensions=Kt(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=Kt(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>Fs(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=Ie(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},At=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new gn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new gn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};u();c();p();m();d();l();var yn=class{constructor(t){this.name=t}};function qs(e){return e instanceof yn}function dp(e){return new yn(e)}u();c();p();m();d();l();u();c();p();m();d();l();var Bs=Symbol(),or=class{constructor(t){if(t!==Bs)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?yi:t}},yi=new or(Bs);function Oe(e){return e instanceof or}var fp={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},js="explicitly `undefined` values are not allowed";function wi({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=At.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:f,previewFeatures:T,globalOmit:v}){let A=new hi({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:f,previewFeatures:T,globalOmit:v});return{modelName:e,action:fp[t],query:sr(r,A)}}function sr({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:Gs(r,n),selection:gp(e,t,i,n)}}function gp(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),bp(e,n)):yp(n,t,r)}function yp(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&hp(n,t,e),wp(n,r,e),n}function hp(e,t,r){for(let[n,i]of Object.entries(t)){if(Oe(i))continue;let o=r.nestSelection(n);if(bi(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=sr(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=sr(i,o)}}function wp(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=Vs(i,n);for(let[s,a]of Object.entries(o)){if(Oe(a))continue;bi(a,r.nestSelection(s));let f=r.findField(s);n?.[s]&&!f||(e[s]=!a)}}function bp(e,t){let r={},n=t.getComputedFields(),i=$s(e,n);for(let[o,s]of Object.entries(i)){if(Oe(s))continue;let a=t.nestSelection(o);bi(s,a);let f=t.findField(o);if(!(n?.[o]&&!f)){if(s===!1||s===void 0||Oe(s)){r[o]=!1;continue}if(s===!0){f?.kind==="object"?r[o]=sr({},a):r[o]=!0;continue}r[o]=sr(s,a)}}return r}function Qs(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(ht(e)){if(rn(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(qs(e))return{$type:"Param",value:e.name};if(Tt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return Ep(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:y.from(r,n,i).toString("base64")}}if(xp(e))return e.values;if(wt(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Ne){if(e!==di.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(Pp(e))return e.toJSON();if(typeof e=="object")return Gs(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function Gs(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);Oe(i)||(i!==void 0?r[n]=Qs(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:js}))}return r}function Ep(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||Oe(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(Qs(o,i))}return r}function xp(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function Pp(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function bi(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:js})}var hi=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){fn({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[Ve(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:xe(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};u();c();p();m();d();l();function Hs(e){if(!e._hasPreviewFlag("metrics"))throw new ie("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var ar=class{_client;constructor(t){this._client=t}prometheus(t){return Hs(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return Hs(this._client),this._client._engine.metrics({format:"json",...t})}};u();c();p();m();d();l();function Tp(e,t){let r=Kt(()=>vp(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function vp(e){throw new Error("Prisma.dmmf is not available when running in edge runtimes.")}function Ei(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}u();c();p();m();d();l();var xi=new WeakMap,hn="$$PrismaTypedSql",lr=class{constructor(t,r){xi.set(this,{sql:t,values:r}),Object.defineProperty(this,hn,{value:hn})}get sql(){return xi.get(this).sql}get values(){return xi.get(this).values}};function Ap(e){return(...t)=>new lr(e,t)}function wn(e){return e!=null&&e[hn]===hn}u();c();p();m();d();l();var Du=_e(Js());u();c();p();m();d();l();Ws();ts();ss();u();c();p();m();d();l();var ye=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let f=0;for(;f<s.values.length;)this.values[o++]=s.values[f++],this.strings[o]=s.strings[f];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function Rp(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new ye([r,...Array(e.length-1).fill(t),n],e)}function Ks(e){return new ye([e],[])}var Sp=Ks("");function zs(e,...t){return new ye(e,t)}u();c();p();m();d();l();u();c();p();m();d();l();function ur(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}u();c();p();m();d();l();function ae(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}u();c();p();m();d();l();function Xe(e){let t=new Re;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}u();c();p();m();d();l();u();c();p();m();d();l();var En={enumerable:!0,configurable:!0,writable:!0};function xn(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>En,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var Ys=Symbol.for("nodejs.util.inspect.custom");function Pe(e,t){let r=Ip(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=Zs(Reflect.ownKeys(o),r),a=Zs(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let f=r.get(s);return f?f.getPropertyDescriptor?{...En,...f?.getPropertyDescriptor(s)}:En:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[Ys]=function(){let o={...this};return delete o[Ys],o},i}function Ip(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function Zs(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}u();c();p();m();d();l();function Ct(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}u();c();p();m();d();l();function Rt(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}u();c();p();m();d();l();function Xs(e){if(e===void 0)return"";let t=vt(e);return new bt(0,{colors:un}).write(t).toString()}u();c();p();m();d();l();var Op="P2037";function Pn({error:e,user_facing_error:t},r,n){return t.error_code?new ee(kp(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new se(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function kp(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===Op&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var Pi=class{getLocation(){return null}};function Be(e){return typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new Pi}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var ea={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function St(e={}){let t=_p(e);return Object.entries(t).reduce((n,[i,o])=>(ea[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function _p(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function Tn(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function ta(e,t){let r=Tn(e);return t({action:"aggregate",unpacker:r,argsMapper:St})(e)}u();c();p();m();d();l();function Mp(e={}){let{select:t,...r}=e;return typeof t=="object"?St({...r,_count:t}):St({...r,_count:{_all:!0}})}function Np(e={}){return typeof e.select=="object"?t=>Tn(e)(t)._count:t=>Tn(e)(t)._count._all}function ra(e,t){return t({action:"count",unpacker:Np(e),argsMapper:Mp})(e)}u();c();p();m();d();l();function Lp(e={}){let t=St(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function Up(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function na(e,t){return t({action:"groupBy",unpacker:Up(e),argsMapper:Lp})(e)}function ia(e,t,r){if(t==="aggregate")return n=>ta(n,r);if(t==="count")return n=>ra(n,r);if(t==="groupBy")return n=>na(n,r)}u();c();p();m();d();l();function oa(e,t){let r=t.fields.filter(i=>!i.relationName),n=bs(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new er(e,o,s.type,s.isList,s.kind==="enum")},...xn(Object.keys(n))})}u();c();p();m();d();l();u();c();p();m();d();l();var sa=e=>Array.isArray(e)?e:e.split("."),Ti=(e,t)=>sa(t).reduce((r,n)=>r&&r[n],e),aa=(e,t,r)=>sa(t).reduceRight((n,i,o,s)=>Object.assign({},Ti(e,s.slice(0,o)),{[i]:n}),r);function Fp(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function $p(e,t,r){return t===void 0?e??{}:aa(t,r,e||!0)}function vi(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((f,T)=>({...f,[T.name]:T}),{});return f=>{let T=Be(e._errorFormat),v=Fp(n,i),A=$p(f,o,v),R=r({dataPath:v,callsite:T})(A),C=Vp(e,t);return new Proxy(R,{get(D,I){if(!C.includes(I))return D[I];let be=[a[I].type,r,I],le=[v,A];return vi(e,...be,...le)},...xn([...C,...Object.getOwnPropertyNames(R)])})}}function Vp(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var qp=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],Bp=["aggregate","count","groupBy"];function Ai(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[jp(e,t),Gp(e,t),ur(r),ae("name",()=>t),ae("$name",()=>t),ae("$parent",()=>e._appliedParent)];return Pe({},n)}function jp(e,t){let r=Ie(t),n=Object.keys(zt).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>f=>{let T=Be(e._errorFormat);return e._createPrismaPromise(v=>{let A={args:f,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:v,callsite:T};return e._request({...A,...a})},{action:o,args:f,model:t})};return qp.includes(o)?vi(e,t,s):Qp(i)?ia(e,i,s):s({})}}}function Qp(e){return Bp.includes(e)}function Gp(e,t){return Xe(ae("fields",()=>{let r=e._runtimeDataModel.models[t];return oa(t,r)}))}u();c();p();m();d();l();function la(e){return e.replace(/^./,t=>t.toUpperCase())}var Ci=Symbol();function cr(e){let t=[Hp(e),Jp(e),ae(Ci,()=>e),ae("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(ur(r)),Pe(e,t)}function Hp(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function Jp(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(Ie),n=[...new Set(t.concat(r))];return Xe({getKeys(){return n},getPropertyValue(i){let o=la(i);if(e._runtimeDataModel.models[o]!==void 0)return Ai(e,o);if(e._runtimeDataModel.models[i]!==void 0)return Ai(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function ua(e){return e[Ci]?e[Ci]:e}function ca(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return cr(t)}u();c();p();m();d();l();u();c();p();m();d();l();function pa({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let f of Object.values(o)){if(n){if(n[f.name])continue;let T=f.needs.filter(v=>n[v]);T.length>0&&a.push(Ct(T))}else if(r){if(!r[f.name])continue;let T=f.needs.filter(v=>!r[v]);T.length>0&&a.push(Ct(T))}Wp(e,f.needs)&&s.push(Kp(f,Pe(e,s)))}return s.length>0||a.length>0?Pe(e,[...s,...a]):e}function Wp(e,t){return t.every(r=>ai(e,r))}function Kp(e,t){return Xe(ae(e.name,()=>e.compute(t)))}u();c();p();m();d();l();function vn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=vn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&ma({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&ma({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function ma({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||Oe(s))continue;let f=n.models[r].fields.find(v=>v.name===o);if(!f||f.kind!=="object"||!f.relationName)continue;let T=typeof s=="object"?s:{};t[o]=vn({visitor:i,result:t[o],args:T,modelName:f.type,runtimeDataModel:n})}}function da({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:vn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,f,T)=>{let v=Ie(f);return pa({result:a,modelName:v,select:T.select,omit:T.select?void 0:{...o?.[v],...T.omit},extensions:n})}})}u();c();p();m();d();l();u();c();p();m();d();l();l();u();c();p();m();d();l();var zp=["$connect","$disconnect","$on","$transaction","$use","$extends"],fa=zp;function ga(e){if(e instanceof ye)return Yp(e);if(wn(e))return Zp(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=pr(e[n]);return r}let t={};for(let r in e)t[r]=pr(e[r]);return t}function Yp(e){return new ye(e.strings,e.values)}function Zp(e){return new lr(e.sql,e.values)}function pr(e){if(typeof e!="object"||e==null||e instanceof Ne||Tt(e))return e;if(wt(e))return new ne(e.toFixed());if(ht(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=pr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:pr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=pr(e[r]);return t}xe(e,"Unknown value")}function ha(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:ga(t.args??{}),__internalParams:t,query:(s,a=t)=>{let f=a.customDataProxyFetch;return a.customDataProxyFetch=xa(o,f),a.args=s,ha(e,a,r,n+1)}})})}function wa(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return ha(e,t,s)}function ba(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?Ea(r,n,0,e):e(r)}}function Ea(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let f=a.customDataProxyFetch;return a.customDataProxyFetch=xa(i,f),Ea(a,t,r+1,n)}})}var ya=e=>e;function xa(e=ya,t=ya){return r=>e(t(r))}u();c();p();m();d();l();var Pa=Y("prisma:client"),Ta={Vercel:"vercel","Netlify CI":"netlify"};function va({postinstall:e,ciName:t,clientVersion:r}){if(Pa("checkPlatformCaching:postinstall",e),Pa("checkPlatformCaching:ciName",t),e===!0&&t&&t in Ta){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${Ta[t]}-build`;throw console.error(n),new U(n,r)}}u();c();p();m();d();l();function Aa(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}u();c();p();m();d();l();u();c();p();m();d();l();var Xp=()=>globalThis.process?.release?.name==="node",em=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,tm=()=>!!globalThis.Deno,rm=()=>typeof globalThis.Netlify=="object",nm=()=>typeof globalThis.EdgeRuntime=="object",im=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function om(){return[[rm,"netlify"],[nm,"edge-light"],[im,"workerd"],[tm,"deno"],[em,"bun"],[Xp,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var sm={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function et(){let e=om();return{id:e,prettyName:sm[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}u();c();p();m();d();l();u();c();p();m();d();l();var Ri=_e(si());u();c();p();m();d();l();function Ca(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}u();c();p();m();d();l();function Ra(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}u();c();p();m();d();l();var Sa=_e(ys());function Ia({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,Sa.default)({user:t,repo:r,template:n,title:e,body:i})}function Oa({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=Yo(6e3-(s?.length??0)),f=Ra((0,Ri.default)(a)),T=n?`# Description
\`\`\`
${n}
\`\`\``:"",v=(0,Ri.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${g.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${T}

## Logs
\`\`\`
${f}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?Ca(s):""}
\`\`\`
`),A=Ia({title:r,body:v});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${Wr(A)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();l();u();c();p();m();d();l();l();function Q(e,t){throw new Error(t)}function Si(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>Si(e[r],t[r]))}function mr(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]!=typeof t[o]){if(typeof e[o]=="number"||typeof t[o]=="number")return`${e[o]}`==`${t[o]}`;if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return BigInt(`${e[o]}`.replace(/n$/,""))===BigInt(`${t[o]}`.replace(/n$/,""));if(e[o]instanceof Date||t[o]instanceof Date)return new Date(`${e[o]}`).getTime()===new Date(`${t[o]}`).getTime();if(ne.isDecimal(e[o])||ne.isDecimal(t[o]))return new ne(`${e[o]}`).equals(new ne(`${t[o]}`))}return Si(e[o],t[o])})}function dr(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():r instanceof Uint8Array?y.from(r).toString("base64"):r)}var J=class extends Error{name="DataMapperError"};function _a(e,t,r){switch(t.type){case"AffectedRows":if(typeof e!="number")throw new J(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"Object":return Ii(e,t.fields,r);case"Value":return Oi(e,"<result>",t.resultType,r);default:Q(t,`Invalid data mapping type: '${t.type}'`)}}function Ii(e,t,r){if(e===null)return null;if(Array.isArray(e))return e.map(i=>ka(i,t,r));if(typeof e=="object")return ka(e,t,r);if(typeof e=="string"){let n;try{n=JSON.parse(e)}catch(i){throw new J("Expected an array or object, got a string that is not valid JSON",{cause:i})}return Ii(n,t,r)}throw new J(`Expected an array or an object, got: ${typeof e}`)}function ka(e,t,r){if(typeof e!="object")throw new J(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"AffectedRows":throw new J(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"Object":{if(!o.flattened&&!Object.hasOwn(e,i))throw new J(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.flattened?e:e[i];n[i]=Ii(s,o.fields,r);break}case"Value":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=Oi(e[s],s,o.resultType,r);else throw new J(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:Q(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function Oi(e,t,r,n){if(e===null)return r.type==="Array"?[]:null;switch(r.type){case"Any":return e;case"String":{if(typeof e!="string")throw new J(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"Int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new J(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new J(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new J(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"BigInt":{if(typeof e!="number"&&typeof e!="string")throw new J(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"Float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new J(`Expected a float in column '${t}', got string: ${e}`);return i}throw new J(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"Boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new J(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}throw new J(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"Decimal":if(typeof e!="number"&&typeof e!="string"&&!ne.isDecimal(e))throw new J(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"Date":{if(typeof e=="string")return{$type:"DateTime",value:Da(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new J(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"Time":{if(typeof e=="string")return{$type:"DateTime",value:`1970-01-01T${Da(e)}`};throw new J(`Expected a time in column '${t}', got ${typeof e}: ${e}`)}case"Array":return e.map((o,s)=>Oi(o,`${t}[${s}]`,r.inner,n));case"Object":return{$type:"Json",value:typeof e=="string"?e:dr(e)};case"Bytes":{if(typeof e=="string"&&e.startsWith("\\x"))return{$type:"Bytes",value:y.from(e.slice(2),"hex").toString("base64")};if(Array.isArray(e))return{$type:"Bytes",value:y.from(e).toString("base64")};throw new J(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`)}case"Enum":{let i=n[r.inner];if(i===void 0)throw new J(`Unknown enum '${r.inner}'`);let o=i[`${e}`];if(o===void 0)throw new J(`Unknown enum value '${e}' for enum '${r.inner}'`);return o}default:Q(r,`DataMapper: Unknown result type: ${r.type}`)}}var am=/Z$|(?<!\d{4}-\d{2})[+-]\d{2}(:?\d{2})?$/;function Da(e){let t=am.exec(e);return t===null?`${e}Z`:t[0]!=="Z"&&t[1]===void 0?`${e}:00`:e}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();var fr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(fr||(fr={}));function lm(e){switch(e){case"postgres":return"postgresql";case"mysql":return"mysql";case"sqlite":return"sqlite";case"sqlserver":return"mssql";default:Q(e,`Unknown provider: ${e}`)}}async function An({query:e,queryable:t,tracingHelper:r,onQuery:n,execute:i}){return await r.runInChildSpan({name:"db_query",kind:fr.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":lm(t.provider)}},async()=>{let o=new Date,s=w.now(),a=await i(),f=w.now();return n?.({timestamp:o,duration:f-s,query:e.sql,params:e.args}),a})}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();function ki(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}u();c();p();m();d();l();var _={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};var ke=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function Ma(e){if(!ki(e))throw e;let t=um(e),r=cm(e);throw!t||!r?e:new ke(r,t,{driverAdapterError:e})}function um(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"TransactionAlreadyClosed":return"P1018";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"ValueOutOfRange":return"P2020";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":case"InconsistentColumnData":return"P2023";case"MissingFullTextSearchIndex":return"P2030";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":case"mssql":return;default:Q(e.cause,`Unknown error: ${e.cause}`)}}function cm(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"TransactionAlreadyClosed":return e.cause.cause;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${Di(e.cause.constraint)}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${Di(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${Di(e.cause.constraint)}`;case"ValueOutOfRange":return`Value out of range for the type. ${e.cause.cause}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Invalid isolation level \`${e.cause.level}\``;case"InconsistentColumnData":return`Inconsistent column data: ${e.cause.cause}`;case"MissingFullTextSearchIndex":return"Cannot find a fulltext index to use for the native search, try adding a @@fulltext([Fields...]) to your schema";case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":case"mssql":return;default:Q(e.cause,`Unknown error: ${e.cause}`)}}function Di(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();u();c();p();m();d();l();function tt(e,t){var r="000000000"+e;return r.substr(r.length-t)}var Na=_e(rs(),1);function pm(){try{return Na.default.hostname()}catch{return g.env._CLUSTER_NETWORK_NAME_||g.env.COMPUTERNAME||"hostname"}}var La=2,mm=tt(g.pid.toString(36),La),Ua=pm(),dm=Ua.length,fm=tt(Ua.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+dm+36).toString(36),La);function _i(){return mm+fm}u();c();p();m();d();l();u();c();p();m();d();l();function Cn(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function Mi(e){let n=Math.pow(36,4),i=0;function o(){return tt((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var f="c",T=new Date().getTime().toString(36),v=tt(s().toString(36),4),A=e(),R=o()+o();return f+T+v+A+R}return a.fingerprint=e,a.isCuid=Cn,a}var gm=Mi(_i);var Fa=gm;var Nl=_e(Sl());u();c();p();m();d();l();Ye();u();c();p();m();d();l();var Il="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var td=128,nt,kt;function rd(e){!nt||nt.length<e?(nt=y.allocUnsafe(e*td),Ht.getRandomValues(nt),kt=0):kt+e>nt.length&&(Ht.getRandomValues(nt),kt=0),kt+=e}function ji(e=21){rd(e|=0);let t="";for(let r=kt-e;r<kt;r++)t+=Il[nt[r]&63];return t}u();c();p();m();d();l();Ye();var kl="0123456789ABCDEFGHJKMNPQRSTVWXYZ",Er=32;var nd=16,Dl=10,Ol=0xffffffffffff;var it;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(it||(it={}));var ot=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function id(e){let t=Math.floor(e()*Er);return t===Er&&(t=Er-1),kl.charAt(t)}function od(e){let t=sd(),r=t&&(t.crypto||t.msCrypto)||(typeof gt<"u"?gt:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(gt?.randomBytes)return()=>gt.randomBytes(1).readUInt8()/255;throw new ot(it.PRNGDetectFailure,"Failed to find a reliable PRNG")}function sd(){return ud()?self:typeof window<"u"?window:typeof globalThis<"u"||typeof globalThis<"u"?globalThis:null}function ad(e,t){let r="";for(;e>0;e--)r=id(t)+r;return r}function ld(e,t=Dl){if(isNaN(e))throw new ot(it.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>Ol)throw new ot(it.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${Ol}: ${e}`);if(e<0)throw new ot(it.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new ot(it.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%Er,n=kl.charAt(r)+n,e=(e-r)/Er;return n}function ud(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function _l(e,t){let r=t||od(),n=!e||isNaN(e)?Date.now():e;return ld(n,Dl)+ad(nd,r)}u();c();p();m();d();l();u();c();p();m();d();l();var re=[];for(let e=0;e<256;++e)re.push((e+256).toString(16).slice(1));function On(e,t=0){return(re[e[t+0]]+re[e[t+1]]+re[e[t+2]]+re[e[t+3]]+"-"+re[e[t+4]]+re[e[t+5]]+"-"+re[e[t+6]]+re[e[t+7]]+"-"+re[e[t+8]]+re[e[t+9]]+"-"+re[e[t+10]]+re[e[t+11]]+re[e[t+12]]+re[e[t+13]]+re[e[t+14]]+re[e[t+15]]).toLowerCase()}u();c();p();m();d();l();Ye();var Dn=new Uint8Array(256),kn=Dn.length;function Dt(){return kn>Dn.length-16&&(Yr(Dn),kn=0),Dn.slice(kn,kn+=16)}u();c();p();m();d();l();u();c();p();m();d();l();Ye();var Qi={randomUUID:zr};function cd(e,t,r){if(Qi.randomUUID&&!t&&!e)return Qi.randomUUID();e=e||{};let n=e.random??e.rng?.()??Dt();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return On(n)}var Gi=cd;u();c();p();m();d();l();var Hi={};function pd(e,t,r){let n;if(e)n=Ml(e.random??e.rng?.()??Dt(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Dt();md(Hi,i,o),n=Ml(o,Hi.msecs,Hi.seq,t,r)}return t??On(n)}function md(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function Ml(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var Ji=pd;var _n=class{#e={};constructor(){this.register("uuid",new zi),this.register("cuid",new Yi),this.register("ulid",new Zi),this.register("nanoid",new Xi),this.register("product",new eo)}snapshot(t){return Object.create(this.#e,{now:{value:t==="mysql"?new Ki:new Wi}})}register(t,r){this.#e[t]=r}},Wi=class{#e=new Date;generate(){return this.#e.toISOString()}},Ki=class{#e=new Date;generate(){return this.#e.toISOString().replace("T"," ").replace("Z","")}},zi=class{generate(t){if(t===4)return Gi();if(t===7)return Ji();throw new Error("Invalid UUID generator arguments")}},Yi=class{generate(t){if(t===1)return Fa();if(t===2)return(0,Nl.createId)();throw new Error("Invalid CUID generator arguments")}},Zi=class{generate(){return _l()}},Xi=class{generate(t){if(typeof t=="number")return ji(t);if(t===void 0)return ji();throw new Error("Invalid Nanoid generator arguments")}},eo=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};u();c();p();m();d();l();u();c();p();m();d();l();function to(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function ro(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function Ll(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bytes"}function Ul(e){return typeof e=="object"&&e!==null&&e.prisma__type==="bigint"}function io(e,t,r){let n=e.type;switch(n){case"rawSql":return $l(e.sql,Fl(e.params,t,r));case"templateSql":return dd(e.fragments,e.placeholderFormat,Fl(e.params,t,r));default:Q(n,"Invalid query type")}}function Fl(e,t,r){return e.map(n=>Te(n,t,r))}function Te(e,t,r){let n=e;for(;gd(n);)if(to(n)){let i=t[n.prisma__value.name];if(i===void 0)throw new Error(`Missing value for query variable ${n.prisma__value.name}`);n=i}else if(ro(n)){let{name:i,args:o}=n.prisma__value,s=r[i];if(!s)throw new Error(`Encountered an unknown generator '${i}'`);n=s.generate(...o.map(a=>Te(a,t,r)))}else Q(n,`Unexpected unevaluated value type: ${n}`);return Array.isArray(n)?n=n.map(i=>Te(i,t,r)):Ll(n)?n=y.from(n.prisma__value,"base64"):Ul(n)&&(n=BigInt(n.prisma__value)),n}function dd(e,t,r){let n=0,i=1,o=[],s=e.map(a=>{let f=a.type;switch(f){case"parameter":if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);return o.push(r[n++]),no(t,i++);case"stringChunk":return a.chunk;case"parameterTuple":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let T=r[n++],v=Array.isArray(T)?T:[T];return`(${v.length==0?"NULL":v.map(R=>(o.push(R),no(t,i++))).join(",")})`}case"parameterTupleList":{if(n>=r.length)throw new Error(`Malformed query template. Fragments attempt to read over ${r.length} parameters.`);let T=r[n++];if(!Array.isArray(T))throw new Error("Malformed query template. Tuple list expected.");if(T.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");return T.map(A=>{if(!Array.isArray(A))throw new Error("Malformed query template. Tuple expected.");let R=A.map(C=>(o.push(C),no(t,i++))).join(a.itemSeparator);return`${a.itemPrefix}${R}${a.itemSuffix}`}).join(a.groupSeparator)}default:Q(f,"Invalid fragment type")}}).join("");return $l(s,o)}function no(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function $l(e,t){let r=t.map(n=>fd(n));return{sql:e,args:t,argTypes:r}}function fd(e){return typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":y.isBuffer(e)?"Bytes":"Unknown"}function gd(e){return to(e)||ro(e)}u();c();p();m();d();l();function ql(e){return e.rows.map(t=>t.reduce((r,n,i)=>{let o=e.columnNames[i].split("."),s=r;for(let a=0;a<o.length;a++){let f=o[a];a===o.length-1?s[f]=n:(s[f]===void 0&&(s[f]={}),s=s[f])}return r},{}))}function Bl(e){let r=e.columnTypes.map(n=>Vl(n)).map(n=>{switch(n){case"int":return i=>i===null?null:typeof i=="number"?i:parseInt(`${i}`,10);case"bigint":return i=>i===null?null:typeof i=="bigint"?i:BigInt(`${i}`);case"json":return i=>typeof i=="string"?JSON.parse(i):i;case"bool":return i=>typeof i=="string"?i==="true"||i==="1":typeof i=="number"?i===1:i;default:return i=>i}});return{columns:e.columnNames,types:e.columnTypes.map(n=>Vl(n)),rows:e.rows.map(n=>n.map((i,o)=>r[o](i)))}}function Vl(e){switch(e){case _.Int32:return"int";case _.Int64:return"bigint";case _.Float:return"float";case _.Double:return"double";case _.Text:return"string";case _.Enum:return"enum";case _.Bytes:return"bytes";case _.Boolean:return"bool";case _.Character:return"char";case _.Numeric:return"decimal";case _.Json:return"json";case _.Uuid:return"uuid";case _.DateTime:return"datetime";case _.Date:return"date";case _.Time:return"time";case _.Int32Array:return"int-array";case _.Int64Array:return"bigint-array";case _.FloatArray:return"float-array";case _.DoubleArray:return"double-array";case _.TextArray:return"string-array";case _.EnumArray:return"string-array";case _.BytesArray:return"bytes-array";case _.BooleanArray:return"bool-array";case _.CharacterArray:return"char-array";case _.NumericArray:return"decimal-array";case _.JsonArray:return"json-array";case _.UuidArray:return"uuid-array";case _.DateTimeArray:return"datetime-array";case _.DateArray:return"date-array";case _.TimeArray:return"time-array";case _.UnknownNumber:return"unknown";case _.Set:return"string";default:Q(e,`Unexpected column type: ${e}`)}}u();c();p();m();d();l();function jl(e,t,r){if(!t.every(n=>oo(e,n))){let n=yd(e,r),i=hd(r);throw new ke(n,i,r.context)}}function oo(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:Q(t,`Unknown rule type: ${t.type}`)}}function yd(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:Q(t,`Unknown error identifier: ${t}`)}}function hd(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:Q(e,`Unknown error identifier: ${e}`)}}var _t=class e{#e;#r;#t;#o=new _n;#n;#s;#i;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s}){this.#e=t,this.#r=r,this.#t=n,this.#n=i,this.#s=o,this.#i=s??o}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:ql,rawSerializer:Bl})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#r,this.#o.snapshot(r.provider)).catch(i=>Ma(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"value":return{value:Te(t.args,n,i)};case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:a}=await this.interpretNode(s.expr,r,o,i);o[s.name]=a}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!Ql(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>s.concat(xr(a)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>De(s)+De(a)):0}}case"execute":{let o=io(t.args,n,i);return this.#a(o,r,async()=>({value:await r.executeRaw(o)}))}case"query":{let o=io(t.args,n,i);return this.#a(o,r,async()=>{let s=await r.queryRaw(o);return t.args.type==="rawSql"?{value:this.#i(s),lastInsertId:s.lastInsertId}:{value:this.#s(s),lastInsertId:s.lastInsertId}})}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(Ql(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:Hl(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let a=await Promise.all(t.args.children.map(async f=>({joinExpr:f,childRecords:(await this.interpretNode(f.child,r,n,i)).value})));return{value:wd(o,a),lastInsertId:s}}case"transaction":{if(!this.#e.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#e.manager,s=await o.startTransaction(),a=o.getTransaction(s,"query");try{let f=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),f}catch(f){throw await o.rollbackTransaction(s.id),f}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:_a(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return jl(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return oo(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),a=new Set(xr(s));return{value:xr(o).filter(f=>!a.has(f))}}case"distinctBy":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=new Set,f=[];for(let T of xr(o)){let v=Mn(T,t.args.fields);a.has(v)||(a.add(v),f.push(T))}return{value:f,lastInsertId:s}}case"paginate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=xr(o),f=t.args.pagination.linkingFields;if(f!==null){let T=new Map;for(let A of a){let R=Mn(A,f);T.has(R)||T.set(R,[]),T.get(R).push(A)}let v=Array.from(T.entries());return v.sort(([A],[R])=>A<R?-1:A>R?1:0),{value:v.flatMap(([,A])=>Gl(A,t.args.pagination)),lastInsertId:s}}return{value:Gl(a,t.args.pagination),lastInsertId:s}}case"initializeRecord":{let{lastInsertId:o}=await this.interpretNode(t.args.expr,r,n,i),s={};for(let[a,f]of Object.entries(t.args.fields))s[a]=bd(f,o,n,i);return{value:s,lastInsertId:o}}case"mapRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=o===null?{}:so(o);for(let[f,T]of Object.entries(t.args.fields))a[f]=Ed(T,a[f],n,i);return{value:a,lastInsertId:s}}default:Q(t,`Unexpected node type: ${t.type}`)}}#a(t,r,n){return An({query:t,queryable:r,execute:n,tracingHelper:this.#n,onQuery:this.#t})}};function Ql(e){return Array.isArray(e)?e.length===0:e==null}function xr(e){return Array.isArray(e)?e:[e]}function De(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function so(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function Hl(e,t){return Array.isArray(e)?e.map(r=>Hl(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function wd(e,t){for(let{joinExpr:r,childRecords:n}of t){let i=r.on.map(([a])=>a),o=r.on.map(([,a])=>a),s={};for(let a of Array.isArray(e)?e:[e]){let f=so(a),T=Mn(f,i);s[T]||(s[T]=[]),s[T].push(f),r.isRelationUnique?f[r.parentField]=null:f[r.parentField]=[]}for(let a of Array.isArray(n)?n:[n]){if(a===null)continue;let f=Mn(so(a),o);for(let T of s[f]??[])r.isRelationUnique?T[r.parentField]=a:T[r.parentField].push(a)}}return e}function Gl(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(a=>mr(a,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function Mn(e,t){return JSON.stringify(t.map(r=>e[r]))}function bd(e,t,r,n){switch(e.type){case"value":return Te(e.value,r,n);case"lastInsertId":return t;default:Q(e,`Unexpected field initializer type: ${e.type}`)}}function Ed(e,t,r,n){switch(e.type){case"set":return Te(e.value,r,n);case"add":return De(t)+De(Te(e.value,r,n));case"subtract":return De(t)-De(Te(e.value,r,n));case"multiply":return De(t)*De(Te(e.value,r,n));case"divide":{let i=De(t),o=De(Te(e.value,r,n));return o===0?null:i/o}default:Q(e,`Unexpected field operation type: ${e.type}`)}}u();c();p();m();d();l();u();c();p();m();d();l();async function xd(){return globalThis.crypto??await Promise.resolve().then(()=>(Ye(),ni))}async function Jl(){return(await xd()).randomUUID()}u();c();p();m();d();l();var we=class extends ke{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},Pr=class extends we{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},Nn=class extends we{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},Ln=class extends we{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},Un=class extends we{constructor(){super("Unable to start a transaction in the given time.")}},Fn=class extends we{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:t,timeout:r,timeTaken:n})}},Mt=class extends we{constructor(t){super(`Internal Consistency Error: ${t}`)}},$n=class extends we{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var Pd=100,Tr=Y("prisma:client:transactionManager"),Td=()=>({sql:"COMMIT",args:[],argTypes:[]}),vd=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),Ad=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),Cd=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),vr=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#e;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#e=i}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.validateOptions(t):this.transactionOptions,n={id:await Jl(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n),n.timer=this.startTransactionTimeout(n.id,r.maxWait);let i=await this.driverAdapter.startTransaction(r.isolationLevel);switch(n.status){case"waiting":return n.transaction=i,clearTimeout(n.timer),n.timer=void 0,n.status="running",n.timer=this.startTransactionTimeout(n.id,r.timeout),{id:n.id};case"timed_out":throw new Un;case"running":case"committed":case"rolled_back":throw new Mt(`Transaction in invalid state ${n.status} although it just finished startup.`);default:Q(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.getActiveTransaction(t,"commit");await this.closeTransaction(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.getActiveTransaction(t,"rollback");await this.closeTransaction(r,"rolled_back")})}getTransaction(t,r){let n=this.getActiveTransaction(t.id,r);if(!n.transaction)throw new Pr;return n.transaction}getActiveTransaction(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(Tr("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"waiting":case"running":throw new Mt("Active transaction found in closed transactions list.");case"committed":throw new Nn(r);case"rolled_back":throw new Ln(r);case"timed_out":throw new Fn(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw Tr("Transaction not found.",t),new Pr}if(["committed","rolled_back","timed_out"].includes(n.status))throw new Mt("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.closeTransaction(t,"rolled_back")))}startTransactionTimeout(t,r){let n=Date.now();return setTimeout(async()=>{Tr("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let i=this.transactions.get(t);i&&["running","waiting"].includes(i.status)?await this.closeTransaction(i,"timed_out"):Tr("Transaction already committed or rolled back when timeout happened.",t)},r)}async closeTransaction(t,r){if(Tr("Closing transaction.",{transactionId:t.id,status:r}),t.status=r,t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#t(Ad(),t.transaction,()=>t.transaction.commit());else{await t.transaction.commit();let n=Td();await this.#t(n,t.transaction,()=>t.transaction.executeRaw(n))}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#t(Cd(),t.transaction,()=>t.transaction.rollback());else{await t.transaction.rollback();let n=vd();await this.#t(n,t.transaction,()=>t.transaction.executeRaw(n))}clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>Pd&&this.closedTransactions.shift()}validateOptions(t){if(!t.timeout)throw new we("timeout is required");if(!t.maxWait)throw new we("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new $n(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#t(t,r,n){return An({query:t,queryable:r,execute:n,tracingHelper:this.tracingHelper,onQuery:this.#e})}};var Vn="6.10.1";u();c();p();m();d();l();var ao,Wl={async loadQueryCompiler(e){let{clientVersion:t,adapter:r,compilerWasm:n}=e;if(r===void 0)throw new U(`The \`adapter\` option for \`PrismaClient\` is required in this context (${et().prettyName})`,t);if(n===void 0)throw new U("WASM query compiler was unexpectedly `undefined`",t);return ao===void 0&&(ao=(async()=>{let i=await n.getRuntime(),o=await n.getQueryCompilerWasmModule();if(o==null)throw new U("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let s={"./query_compiler_bg.js":i},a=new WebAssembly.Instance(o,s),f=a.exports.__wbindgen_start;return i.__wbg_set_wasm(a.exports),f(),i.QueryCompiler})()),await ao}};var Kl="P2038",qn=Y("prisma:client:clientEngine"),Yl=globalThis;Yl.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new pe(e,Vn)}};var Ar=class{name="ClientEngine";queryCompiler;instantiateQueryCompilerPromise;QueryCompilerConstructor;queryCompilerLoader;adapterPromise;transactionManagerPromise;config;provider;datamodel;logEmitter;logQueries;logLevel;lastStartedQuery;tracingHelper;#e;constructor(t,r){if(!t.previewFeatures?.includes("driverAdapters"))throw new U("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,Kl);if(t.adapter)this.adapterPromise=t.adapter.connect(),this.provider=t.adapter.provider,qn("Using driver adapter: %O",t.adapter);else throw new U("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,Kl);this.queryCompilerLoader=r??Wl,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#e=n=>{this.logEmitter.emit("query",{...n,params:dr(n.params),target:"ClientEngine"})}),this.transactionManagerPromise=this.adapterPromise.then(n=>new vr({driverAdapter:n,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#i(this.config.transactionOptions.isolationLevel)},tracingHelper:this.tracingHelper,onQuery:this.#e})),this.instantiateQueryCompilerPromise=this.instantiateQueryCompiler()}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async instantiateQueryCompiler(){if(this.queryCompiler)return;this.QueryCompilerConstructor||(this.QueryCompilerConstructor=await this.queryCompilerLoader.loadQueryCompiler(this.config));let r=(await this.adapterPromise)?.getConnectionInfo?.()??{supportsRelationJoins:!1};try{this.#n(()=>{this.queryCompiler=new this.QueryCompilerConstructor({datamodel:this.datamodel,provider:this.provider,connectionInfo:r})})}catch(n){throw this.#r(n)}}#r(t){if(t instanceof pe)return t;try{let r=JSON.parse(t.message);return new U(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#t(t){if(t instanceof U)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new pe(zl(this,t.message),this.config.clientVersion);if(t instanceof ke)return new ee(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let r=JSON.parse(t);return new se(`${r.message}
${r.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#o(t){return t instanceof pe?t:typeof t.message=="string"&&typeof t.code=="string"?new ee(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):t}#n(t){let r=Yl.PRISMA_WASM_PANIC_REGISTRY.set_message,n;globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=i=>{n=i};try{return t()}finally{if(globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=r,n)throw new pe(zl(this,n),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.tracingHelper.runInChildSpan("connect",()=>this.ensureStarted())}async stop(){await this.tracingHelper.runInChildSpan("disconnect",async()=>{await this.instantiateQueryCompilerPromise,await(await this.transactionManagerPromise)?.cancelAllTransactions(),await(await this.adapterPromise).dispose()})}async ensureStarted(){let t=await this.adapterPromise,r=await this.transactionManagerPromise;return await this.instantiateQueryCompilerPromise,[t,r]}version(){return"unknown"}async transaction(t,r,n){let i,o=await this.transactionManagerPromise;try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#i(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s.id)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s.id)}else xe(t,"Invalid transaction action.")}catch(s){throw this.#t(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{traceparent:r,interactiveTransaction:n}){qn("sending request");let i=JSON.stringify(t);this.lastStartedQuery=i;let[o,s]=await this.ensureStarted().catch(f=>{throw this.#t(f)}),a;try{a=this.#n(()=>this.queryCompiler.compile(i))}catch(f){throw this.#o(f)}try{qn("query plan created",a);let f=n?s.getTransaction(n,"query"):o,T=n?{enabled:!1}:{enabled:!0,manager:s},v={},R=await _t.forSql({transactionManager:T,placeholderValues:v,onQuery:this.#e,tracingHelper:this.tracingHelper}).run(a,f);return qn("query plan executed"),{data:{[t.action]:R}}}catch(f){throw this.#t(f)}}async requestBatch(t,{transaction:r,traceparent:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(Rt(t,r));this.lastStartedQuery=o;let[,s]=await this.ensureStarted().catch(f=>{throw this.#t(f)}),a;try{a=this.queryCompiler.compileBatch(o)}catch(f){throw this.#o(f)}try{let f;if(r?.kind==="itx")f=r.options;else{let C=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;f=await this.transaction("start",{},C)}let T={},v=_t.forSql({transactionManager:{enabled:!1},placeholderValues:T,onQuery:this.#e,tracingHelper:this.tracingHelper}),A=s.getTransaction(f,"batch query"),R=[];switch(a.type){case"multi":{R=await Promise.all(a.plans.map((C,D)=>v.run(C,A).then(I=>({data:{[t[D].action]:I}}),I=>I)));break}case"compacted":{if(!t.every(D=>D.action===i))throw new Error("All queries in a batch must have the same action");let C=await v.run(a.plan,A);R=this.#s(C,a,i);break}}return r?.kind!=="itx"&&await this.transaction("commit",{},f),R}catch(f){throw this.#t(f)}}metrics(t){throw new Error("Method not implemented.")}#s(t,r,n){let i=t.map(s=>r.keys.reduce((a,f)=>(a[f]=Ze(s[f]),a),{})),o=new Set(r.nestedSelection);return r.arguments.map(s=>{let a=i.findIndex(f=>mr(f,s));if(a===-1)return r.expectNonEmpty?new ee("An operation failed because it depends on one or more records that were required but not found",{code:"P2025",clientVersion:this.config.clientVersion}):{data:{[n]:null}};{let f=Object.entries(t[a]).filter(([T])=>o.has(T));return{data:{[n]:Object.fromEntries(f)}}}})}#i(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new ee(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}};function zl(e,t){return Oa({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:e.lastStartedQuery})}u();c();p();m();d();l();u();c();p();m();d();l();function Nt({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw et().id==="workerd"?new U(`error: Environment variable not found: ${s.fromEnvVar}.

In Cloudflare module Workers, environment variables are available only in the Worker's \`env\` parameter of \`fetch\`.
To solve this, provide the connection string directly: https://pris.ly/d/cloudflare-datasource-url`,n):new U(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new U("error: Missing URL environment variable, value, or override.",n);return i}u();c();p();m();d();l();u();c();p();m();d();l();var Bn=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var me=class extends Bn{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};u();c();p();m();d();l();u();c();p();m();d();l();function N(e,t){return{...e,isRetryable:t}}var Lt=class extends me{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",N(t,!0))}};O(Lt,"ForcedRetryError");u();c();p();m();d();l();var st=class extends me{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,N(r,!1))}};O(st,"InvalidDatasourceError");u();c();p();m();d();l();var at=class extends me{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,N(r,!1))}};O(at,"NotImplementedYetError");u();c();p();m();d();l();u();c();p();m();d();l();var B=class extends me{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var lt=class extends B{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",N(t,!0))}};O(lt,"SchemaMissingError");u();c();p();m();d();l();u();c();p();m();d();l();var lo="This request could not be understood by the server",Cr=class extends B{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||lo,N(t,!1)),n&&(this.code=n)}};O(Cr,"BadRequestError");u();c();p();m();d();l();var Rr=class extends B{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",N(t,!0)),this.logs=r}};O(Rr,"HealthcheckTimeoutError");u();c();p();m();d();l();var Sr=class extends B{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,N(t,!0)),this.logs=n}};O(Sr,"EngineStartupError");u();c();p();m();d();l();var Ir=class extends B{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",N(t,!1))}};O(Ir,"EngineVersionNotSupportedError");u();c();p();m();d();l();var uo="Request timed out",Or=class extends B{name="GatewayTimeoutError";code="P5009";constructor(t,r=uo){super(r,N(t,!1))}};O(Or,"GatewayTimeoutError");u();c();p();m();d();l();var Sd="Interactive transaction error",kr=class extends B{name="InteractiveTransactionError";code="P5015";constructor(t,r=Sd){super(r,N(t,!1))}};O(kr,"InteractiveTransactionError");u();c();p();m();d();l();var Id="Request parameters are invalid",Dr=class extends B{name="InvalidRequestError";code="P5011";constructor(t,r=Id){super(r,N(t,!1))}};O(Dr,"InvalidRequestError");u();c();p();m();d();l();var co="Requested resource does not exist",_r=class extends B{name="NotFoundError";code="P5003";constructor(t,r=co){super(r,N(t,!1))}};O(_r,"NotFoundError");u();c();p();m();d();l();var po="Unknown server error",Ut=class extends B{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||po,N(t,!0)),this.logs=n}};O(Ut,"ServerError");u();c();p();m();d();l();var mo="Unauthorized, check your connection string",Mr=class extends B{name="UnauthorizedError";code="P5007";constructor(t,r=mo){super(r,N(t,!1))}};O(Mr,"UnauthorizedError");u();c();p();m();d();l();var fo="Usage exceeded, retry again later",Nr=class extends B{name="UsageExceededError";code="P5008";constructor(t,r=fo){super(r,N(t,!0))}};O(Nr,"UsageExceededError");async function Od(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function Lr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await Od(e);if(n.type==="QueryEngineError")throw new ee(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new Ut(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new lt(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Ir(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Sr(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new U(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new Rr(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new kr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Dr(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new Mr(r,Ft(mo,n));if(e.status===404)return new _r(r,Ft(co,n));if(e.status===429)throw new Nr(r,Ft(fo,n));if(e.status===504)throw new Or(r,Ft(uo,n));if(e.status>=500)throw new Ut(r,Ft(po,n));if(e.status>=400)throw new Cr(r,Ft(lo,n))}function Ft(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}u();c();p();m();d();l();function Zl(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}u();c();p();m();d();l();var Fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function Xl(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,f,T,v;for(let A=0;A<o;A=A+3)v=t[A]<<16|t[A+1]<<8|t[A+2],s=(v&16515072)>>18,a=(v&258048)>>12,f=(v&4032)>>6,T=v&63,r+=Fe[s]+Fe[a]+Fe[f]+Fe[T];return i==1?(v=t[o],s=(v&252)>>2,a=(v&3)<<4,r+=Fe[s]+Fe[a]+"=="):i==2&&(v=t[o]<<8|t[o+1],s=(v&64512)>>10,a=(v&1008)>>4,f=(v&15)<<2,r+=Fe[s]+Fe[a]+Fe[f]+"="),r}u();c();p();m();d();l();function eu(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new U("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}u();c();p();m();d();l();function kd(e){return e[0]*1e3+e[1]/1e6}function go(e){return new Date(kd(e))}u();c();p();m();d();l();var tu={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.10.1-1.9b628578b3b7cae625e8c927178f15a170e74a9c","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};u();c();p();m();d();l();u();c();p();m();d();l();var Ur=class extends me{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,N(r,!0))}};O(Ur,"RequestError");async function ut(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new Ur(a,{clientVersion:n,cause:s})}}var _d=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,ru=Y("prisma:client:dataproxyEngine");async function Md(e,t){let r=tu["@prisma/engines-version"],n=t.clientVersion??"unknown";if(g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&_d.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,f,T]=s.split("."),v=Nd(`<=${a}.${f}.${T}`),A=await ut(v,{clientVersion:n});if(!A.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${A.status} ${A.statusText}, response body: ${await A.text()||"<empty body>"}`);let R=await A.text();ru("length of body fetched from unpkg.com",R.length);let C;try{C=JSON.parse(R)}catch(D){throw console.error("JSON.parse error: body fetched from unpkg.com: ",R),D}return C.version}throw new at("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function nu(e,t){let r=await Md(e,t);return ru("version",r),r}function Nd(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var iu=3,Fr=Y("prisma:client:dataproxyEngine"),yo=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,interactiveTransaction:r}={}){let n={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-transaction-id"]=r.id);let i=this.buildCaptureSettings();return i.length>0&&(n["X-capture-telemetry"]=i.join(", ")),n}buildCaptureSettings(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}},$r=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){eu(t),this.config=t,this.env={...t.env,...typeof g<"u"?g.env:{}},this.inlineSchema=Xl(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.headerBuilder=new yo({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.protocol=ii(r)?"http":"https",this.remoteClientVersion=await nu(this.host,this.config),Fr("host",this.host),Fr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":Fr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:go(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:go(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await ut(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||Fr("schema response status",r.status);let n=await Lr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=Rt(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(f=>(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f?this.convertProtocolErrorsToClientError(f.errors):f))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await ut(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,interactiveTransaction:i}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||Fr("graphql response status",a.status),await this.handleError(await Lr(a,this.clientVersion));let f=await a.json();if(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f)throw this.convertProtocolErrorsToClientError(f.errors);return"batchResult"in f?f.batchResult:f}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let f=await ut(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await Lr(f,this.clientVersion));let T=await f.json(),{extensions:v}=T;v&&this.propagateResponseExtensions(v);let A=T.id,R=T["data-proxy"].endpoint;return{id:A,payload:{endpoint:R}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await ut(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await Lr(a,this.clientVersion));let f=await a.json(),{extensions:T}=f;T&&this.propagateResponseExtensions(T);return}}})}getURLAndAPIKey(){let t={clientVersion:this.clientVersion},r=Object.keys(this.inlineDatasources)[0],n=Nt({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),i;try{i=new URL(n)}catch{throw new st(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==Zr)throw new st(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new st(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);return{apiKey:a,url:i}}metrics(){throw new at("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof me)||!i.isRetryable)throw i;if(r>=iu)throw i instanceof Lt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${iu} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await Zl(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof lt)throw await this.uploadSchema(),new Lt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?Pn(t[0],this.config.clientVersion,this.config.activeProvider):new se(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};function ou({copyEngine:e=!0},t){let r;try{r=Nt({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...g.env},clientVersion:t.clientVersion})}catch{}let n=!!(r?.startsWith("prisma://")||Xr(r));e&&n&&tn("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)");let i=ft(t.generator),o=n||!e,s=!!t.adapter,a=i==="library",f=i==="binary",T=i==="client";if(o&&s||s&&!1){let v;throw e?r?.startsWith("prisma://")?v=["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:v=["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:v=["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."],new ie(v.join(`
`),{clientVersion:t.clientVersion})}if(o)return new $r(t);if(T)return new Ar(t);{let v=[`PrismaClient failed to initialize because it wasn't configured to run in this environment (${et().prettyName}).`,"In order to run Prisma Client in an edge runtime, you will need to configure one of the following options:","- Enable Driver Adapters: https://pris.ly/d/driver-adapters","- Enable Accelerate: https://pris.ly/d/accelerate"];throw new ie(v.join(`
`),{clientVersion:t.clientVersion})}return"wasm-compiler-edge"}u();c();p();m();d();l();function jn({generator:e}){return e?.previewFeatures??[]}u();c();p();m();d();l();var su=e=>({command:e});u();c();p();m();d();l();u();c();p();m();d();l();var au=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);u();c();p();m();d();l();l();function $t(e){try{return lu(e,"fast")}catch{return lu(e,"slow")}}function lu(e,t){return JSON.stringify(e.map(r=>cu(r,t)))}function cu(e,t){if(Array.isArray(e))return e.map(r=>cu(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(ht(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(ne.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(y.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(Ld(e))return{prisma__type:"bytes",prisma__value:y.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:y.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?pu(e):e}function Ld(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function pu(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(uu);let t={};for(let r of Object.keys(e))t[r]=uu(e[r]);return t}function uu(e){return typeof e=="bigint"?e.toString():pu(e)}var Ud=/^(\s*alter\s)/i,mu=Y("prisma:client");function ho(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&Ud.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var wo=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(wn(r))n=r.sql,i={values:$t(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:$t(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:$t(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:$t(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=au(r),i={values:$t(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?mu(`prisma.${e}(${n}, ${i.values})`):mu(`prisma.${e}(${n})`),{query:n,parameters:i}},du={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new ye(t,r)}},fu={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};u();c();p();m();d();l();function bo(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=gu(r(s)):gu(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function gu(e){return typeof e.then=="function"?e:Promise.resolve(e)}u();c();p();m();d();l();var Fd=ri.split(".")[0],$d={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Eo=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${Fd}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??$d}};function yu(){return new Eo}u();c();p();m();d();l();function hu(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}u();c();p();m();d();l();function wu(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}u();c();p();m();d();l();var Qn=class{_middlewares=[];use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};u();c();p();m();d();l();var Eu=_e(si());u();c();p();m();d();l();function Gn(e){return typeof e.batchRequestIdx=="number"}u();c();p();m();d();l();function bu(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(xo(e.query.arguments)),t.push(xo(e.query.selection)),t.join("")}function xo(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${xo(n)})`:r}).join(" ")})`}u();c();p();m();d();l();var Vd={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Po(e){return Vd[e]}u();c();p();m();d();l();var Hn=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,g.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};u();c();p();m();d();l();l();function ct(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new ne(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>ct("bigint",r));case"bytes-array":return t.map(r=>ct("bytes",r));case"decimal-array":return t.map(r=>ct("decimal",r));case"datetime-array":return t.map(r=>ct("datetime",r));case"date-array":return t.map(r=>ct("date",r));case"time-array":return t.map(r=>ct("time",r));default:return t}}function To(e){let t=[],r=qd(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=ct(e.types[s],i[s]);t.push(o)}return t}function qd(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var Bd=Y("prisma:client:request_handler"),Jn=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new Hn({batchLoader:ba(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(A=>A.protocolQuery),f=this.client._tracingHelper.getTraceParent(s),T=n.some(A=>Po(A.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:f,transaction:jd(o),containsWrite:T,customDataProxyFetch:i})).map((A,R)=>{if(A instanceof Error)return A;try{return this.mapQueryEngineResult(n[R],A)}catch(C){return C}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?xu(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Po(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:bu(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return g.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(Bd(t),Qd(t,i))throw t;if(t instanceof ee&&Gd(t)){let T=Pu(t.meta);fn({args:o,errors:[T],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let f=t.message;if(n&&(f=on({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:f})),f=this.sanitizeMessage(f),t.code){let T=s?{modelName:s,...t.meta}:t.meta;throw new ee(f,{code:t.code,clientVersion:this.client._clientVersion,meta:T,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new pe(f,this.client._clientVersion);if(t instanceof se)throw new se(f,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof U)throw new U(f,this.client._clientVersion);if(t instanceof pe)throw new pe(f,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,Eu.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(T=>T!=="select"&&T!=="include"),a=Ti(o,s),f=i==="queryRaw"?To(a):Ze(a);return n?n(f):f}get[Symbol.toStringTag](){return"RequestHandler"}};function jd(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:xu(e)};xe(e,"Unknown transaction kind")}}function xu(e){return{id:e.id,payload:e.payload}}function Qd(e,t){return Gn(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function Gd(e){return e.code==="P2009"||e.code==="P2012"}function Pu(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Pu)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}u();c();p();m();d();l();var Tu=Vn;u();c();p();m();d();l();var Su=_e(ci());u();c();p();m();d();l();var F=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};O(F,"PrismaClientConstructorValidationError");var vu=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],Au=["pretty","colorless","minimal"],Cu=["info","query","warn","error"],Hd={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new F(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Vt(r,t)||` Available datasources: ${t.join(", ")}`;throw new F(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new F(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new F(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new F(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&ft(t.generator)==="client")throw new F('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new F('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!jn(t).includes("driverAdapters"))throw new F('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(ft(t.generator)==="binary")throw new F('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new F(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new F(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!Au.includes(e)){let t=Vt(e,Au);throw new F(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new F(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Cu.includes(r)){let n=Vt(r,Cu);throw new F(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Vt(i,o);throw new F(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new F(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new F(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new F(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new F('"omit" option is expected to be an object.');if(e===null)throw new F('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=Wd(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let f=o.fields.find(T=>T.name===s);if(!f){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(f.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new F(Kd(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new F(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Vt(r,t);throw new F(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Iu(e,t){for(let[r,n]of Object.entries(e)){if(!vu.includes(r)){let i=Vt(r,vu);throw new F(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Hd[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new F('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Vt(e,t){if(t.length===0||typeof e!="string")return"";let r=Jd(e,t);return r?` Did you mean "${r}"?`:""}function Jd(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,Su.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function Wd(e,t){return Ru(t.models,e)??Ru(t.types,e)}function Ru(e,t){let r=Object.keys(e).find(n=>Ve(n)===t);if(r)return e[r]}function Kd(e,t){let r=vt(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=dn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}u();c();p();m();d();l();function Ou(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},f=T=>{o||(o=!0,r(T))};for(let T=0;T<e.length;T++)e[T].then(v=>{n[T]=v,a()},v=>{if(!Gn(v)){f(v);return}v.batchRequestIdx===T?f(v):(i||(i=v),a())})})}var He=Y("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var zd={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Yd=Symbol.for("prisma.client.transaction.id"),Zd={id:0,nextId(){return++this.id}};function Xd(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_middlewares=new Qn;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=bo();constructor(n){e=n?.__internal?.configOverride?.(e)??e,va(e),n&&Iu(n,e);let i=new bn().on("error",()=>{});this._extensions=At.empty(),this._previewFeatures=jn(e),this._clientVersion=e.clientVersion??Tu,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=yu();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&Kr.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&Kr.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let f=e.activeProvider==="postgresql"?"postgres":e.activeProvider;if(s.provider!==f)throw new U(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${f}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new U("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=e.injectableEdgeEnv?.();try{let f=n??{},T=f.__internal??{},v=T.debug===!0;v&&Y.enable("prisma:client");let A=Kr.resolve(e.dirname,e.relativePath);es.existsSync(A)||(A=e.dirname),He("dirname",e.dirname),He("relativePath",e.relativePath),He("cwd",A);let R=T.engine||{};if(f.errorFormat?this._errorFormat=f.errorFormat:g.env.NODE_ENV==="production"?this._errorFormat="minimal":g.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:A,dirname:e.dirname,enableDebugLogs:v,allowTriggerPanic:R.allowTriggerPanic,prismaPath:R.binaryPath??void 0,engineEndpoint:R.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:f.log&&wu(f.log),logQueries:f.log&&!!(typeof f.log=="string"?f.log==="query":f.log.find(C=>typeof C=="string"?C==="query":C.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:Aa(f,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:f.transactionOptions?.maxWait??2e3,timeout:f.transactionOptions?.timeout??5e3,isolationLevel:f.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Nt,getBatchRequestPayload:Rt,prismaGraphQLToJSError:Pn,PrismaClientUnknownRequestError:se,PrismaClientInitializationError:U,PrismaClientKnownRequestError:ee,debug:Y("prisma:client:accelerateEngine"),engineVersion:Du.version,clientVersion:e.clientVersion}},He("clientVersion",e.clientVersion),this._engine=ou(e,this._engineConfig),this._requestHandler=new Jn(this,i),f.log)for(let C of f.log){let D=typeof C=="string"?C:C.emit==="stdout"?C.level:null;D&&this.$on(D,I=>{Wt.log(`${Wt.tags[D]??""}`,I.message||I.query)})}}catch(f){throw f.clientVersion=this._clientVersion,f}return this._appliedParent=cr(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{Zo()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:wo({clientMethod:i,activeProvider:a}),callsite:Be(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=ku(n,i);return ho(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new ie("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(ho(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new ie(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:su,callsite:Be(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:wo({clientMethod:i,activeProvider:a}),callsite:Be(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...ku(n,i));throw new ie("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new ie("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=Zd.nextId(),s=hu(n.length),a=n.map((f,T)=>{if(f?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let v=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,A={kind:"batch",id:o,index:T,isolationLevel:v,lock:s};return f.requestTransaction?.(A)??f});return Ou(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),f;try{let T={kind:"itx",...a};f=await n(this._createItxClient(T)),await this._engine.transaction("commit",o,a)}catch(T){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),T}return f}_createItxClient(n){return Pe(cr(Pe(ua(this),[ae("_appliedParent",()=>this._appliedParent._createItxClient(n)),ae("_createPrismaPromise",()=>bo(n)),ae(Yd,()=>n.id)])),[Ct(fa)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??zd,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,f=async T=>{let v=this._middlewares.get(++a);if(v)return this._tracingHelper.runInChildSpan(s.middleware,M=>v(T,be=>(M?.end(),f(be))));let{runInTransaction:A,args:R,...C}=T,D={...n,...C};R&&(D.args=i.middlewareArgsToRequestArgs(R)),n.transaction!==void 0&&A===!1&&delete D.transaction;let I=await wa(this,D);return D.model?da({result:I,modelName:D.model,args:D.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):I};return this._tracingHelper.runInChildSpan(s.operation,()=>f(o))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:f,argsMapper:T,transaction:v,unpacker:A,otelParentCtx:R,customDataProxyFetch:C}){try{n=T?T(n):n;let D={name:"serialize"},I=this._tracingHelper.runInChildSpan(D,()=>wi({modelName:f,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return Y.enabled("prisma:client")&&(He("Prisma Client call:"),He(`prisma.${i}(${Xs(n)})`),He("Generated request:"),He(JSON.stringify(I,null,2)+`
`)),v?.kind==="batch"&&await v.lock,this._requestHandler.request({protocolQuery:I,modelName:f,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:v,unpacker:A,otelParentCtx:R,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:C})}catch(D){throw D.clientVersion=this._clientVersion,D}}$metrics=new ar(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=ca}return t}function ku(e,t){return ef(e)?[new ye(e,t),du]:[e,fu]}function ef(e){return Array.isArray(e)&&Array.isArray(e.raw)}u();c();p();m();d();l();var tf=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function rf(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!tf.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}u();c();p();m();d();l();l();var export_warnEnvConflicts=void 0;export{nn as DMMF,Y as Debug,ne as Decimal,Uo as Extensions,ar as MetricsClient,U as PrismaClientInitializationError,ee as PrismaClientKnownRequestError,pe as PrismaClientRustPanicError,se as PrismaClientUnknownRequestError,ie as PrismaClientValidationError,$o as Public,ye as Sql,dp as createParam,Tp as defineDmmfProperty,Ze as deserializeJsonResponse,To as deserializeRawResult,Dc as dmmfToRuntimeDataModel,Sp as empty,Xd as getPrismaClient,et as getRuntime,Rp as join,rf as makeStrictEnum,Ap as makeTypedQueryFactory,di as objectEnumValues,Ks as raw,wi as serializeJsonQuery,yi as skip,zs as sqltag,export_warnEnvConflicts as warnEnvConflicts,tn as warnOnce};
//# sourceMappingURL=wasm-compiler-edge.mjs.map
